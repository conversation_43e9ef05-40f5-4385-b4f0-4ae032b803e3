<template>
  <!-- 主布局容器：左侧导航栏 + 右侧内容区域 -->
  <div class="main-layout">
    <!-- 左侧导航栏 -->
    <div class="sidebar">
      <Sidebar
        :current-module="currentModule"
        @module-change="handleModuleChange"
      />

      <!-- 顶部工具栏 -->
      <div class="toolbar">
        <UserLogin
          :force-login="true"
          @login-success="handleUserLogin"
          @logout="handleUserLogout"
        />
        <!-- Redis连接图标已隐藏 -->
        <!-- <StorageStatusIndicator
          :storage-status="storageStatus"
          :storage-service="storageService"
          :connection-manager="redisConnectionManager"
          @reconnect="handleRedisReconnect"
          @migrate="handleDataMigration"
          @show-info="showStorageInfo"
        /> -->
        <ThemeToggle />
      </div>

      <!-- 移动端悬浮工具栏 -->
      <div class="mobile-toolbar">
        <!-- 手机模式下隐藏登录按钮 -->
        <!-- <UserLogin
          :force-login="true"
          @login-success="handleUserLogin"
          @logout="handleUserLogout"
          class="mobile-toolbar-item"
        /> -->
        <!-- Redis连接图标已隐藏 -->
        <!-- <StorageStatusIndicator
          :storage-status="storageStatus"
          :storage-service="storageService"
          :connection-manager="redisConnectionManager"
          @reconnect="handleRedisReconnect"
          @migrate="handleDataMigration"
          @show-info="showStorageInfo"
          class="mobile-toolbar-item"
        /> -->
        <ThemeToggle class="mobile-toolbar-item" />
      </div>
    </div>
    
    <!-- 右侧内容区域 -->
    <div class="content-area">
      <!-- 帮助与反馈模块不显示会话面板 -->
      <div
        v-if="needSessionPanel"
        class="session-panel"
        :class="{ 'collapsed': isSessionPanelCollapsed }"
        @touchstart="handleSessionPanelTouchStart"
        @touchmove="handleSessionPanelTouchMove"
        @touchend="handleSessionPanelTouchEnd"
      >
        <!-- 折叠切换按钮 -->
        <button
          class="session-panel-toggle"
          @click="handleToggleSessionPanel"
          :title="isSessionPanelCollapsed ? '展开会话面板' : '折叠会话面板'"
        >
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path v-if="isSessionPanelCollapsed" d="M9,5V19L16,12L9,5Z" />
            <path v-else d="M15,5V19L8,12L15,5Z" />
          </svg>
        </button>

        <SessionPanel
          :sessions="sortedCurrentModuleSessions"
          :current-session="currentSession"
          :collapsed="isSessionPanelCollapsed"
          @new-session="handleNewSession"
          @session-change="handleSessionChange"
          @delete-session="handleDeleteSession"
          @rename-session="handleRenameSession"
          @toggle-pin="handleTogglePin"
          @mobile-close="handleMobileCloseSessionPanel"
        />
      </div>

      <!-- 移动端会话管理按钮 -->
      <button
        v-if="needSessionPanel && isMobile"
        class="mobile-session-button"
        @click="handleToggleSessionPanel"
        :title="isSessionPanelCollapsed ? '打开会话管理' : '关闭会话管理'"
      >
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path v-if="isSessionPanelCollapsed" d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
          <path v-else d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
        </svg>
        <span class="button-text">{{ isSessionPanelCollapsed ? '会话' : '关闭' }}</span>
      </button>

      <div class="chat-area" :class="{
        'full-width': !needSessionPanel,
        'session-panel-collapsed': needSessionPanel && isSessionPanelCollapsed
      }">
        <!-- 数据加载完成前显示加载状态 -->
        <div v-if="!sessionsLoaded" class="loading-container">
          <div class="loading-spinner"></div>
          <div class="loading-text">正在加载会话数据...</div>
        </div>

        <!-- 数据加载完成后渲染页面组件 -->
        <component
          v-else
          :is="currentComponent"
          :key="`${currentModule}-${componentKey}`"
          :current-session="currentSession"
          :messages="currentMessages"
          @send-message="handleSendMessage"
          @clear-messages="handleClearMessages"
        />
      </div>
    </div>

    <!-- 通知组件 -->
    <div v-if="notification.show" class="notification" :class="`notification-${notification.type}`">
      <div class="notification-content">
        <span class="notification-message">{{ notification.message }}</span>
        <button class="notification-close" @click="notification.show = false">×</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import Sidebar from './Sidebar.vue'
import SessionPanel from './SessionPanel.vue'
import ThemeToggle from '../common/ThemeToggle.vue'
import UserLogin from '../common/UserLogin.vue'
import StorageStatusIndicator from '../common/StorageStatusIndicator.vue'
import KnowledgeCenter from '../pages/KnowledgeCenter.vue'
import BusinessDomain from '../pages/BusinessDomain.vue'
import FunctionDomain from '../pages/FunctionDomain.vue'
import HelpFeedback from '../pages/HelpFeedback.vue'
import { getAIService } from '../../services/aiService.js'
import sessionStorageService from '../../services/sessionStorageService.js'
import { getRedisSessionStorageService } from '../../services/redisSessionStorageService.js'
import { getRedisConnectionManager } from '../../services/redisConnectionManager.js'
import { getSessionMigrationTool } from '../../utils/sessionMigrationTool.js'
import { getSessionSyncService } from '../../services/sessionSyncService.js'
import { getUserService } from '../../services/userService.js'
import { useResponsive } from '../../composables/useResponsive.js'
import { useTouchGestures } from '../../composables/useTouchGestures.js'
import { refreshAllDynamicWatermarks } from '../../config/watermark.js'

// 响应式设备检测
const { isMobile, isTablet } = useResponsive()

// 触摸手势支持（移动端）
const touchGestures = useTouchGestures({
  threshold: 50,
  preventScroll: false
})

// 会话面板折叠状态
const isSessionPanelCollapsed = ref(false)

// 根据设备类型设置默认折叠状态
watch([isMobile, isTablet], ([mobile, tablet]) => {
  // 手机端默认折叠，平板和桌面端默认不折叠
  if (mobile) {
    isSessionPanelCollapsed.value = true
  } else {
    isSessionPanelCollapsed.value = false
  }
}, { immediate: true })

// 当前选中的模块，默认为知识中心
const currentModule = ref('knowledge')

// 各模块独立的会话数据  可以添加单引号或双引号 "knowledge"
const moduleSessions = ref({
  knowledge: [], // 知识中心会话
  business: [],  // 业务域会话
  function: []   // 职能域会话
  // help 模块不需要会话管理
})

// 各模块当前选中的会话
const moduleCurrentSession = ref({
  knowledge: null,
  business: null,
  function: null
})

// 会话数据是否已加载
const sessionsLoaded = ref(false)

// 用户登录状态
// 临时设置为已登录状态用于测试会话创建功能
// TODO: 恢复为 false
const isUserLoggedIn = ref(true)
const userService = getUserService()

// 存储服务相关状态
const storageService = ref(null)
const redisConnectionManager = ref(null)
const sessionSyncService = ref(null)
const useRedisStorage = ref(false)
const storageStatus = ref({
  type: 'localStorage', // 'localStorage' | 'redis'
  connected: false,
  error: null
})

// 自动保存定时器
let autoSaveTimer = null

// 通知相关状态
const notification = ref({
  show: false,
  message: '',
  type: 'info' // info, success, warning, error
})

// 通知定时器
let notificationTimer = null

// 强制重新渲染的key
const componentKey = ref(0)

// 当前会话（根据当前模块动态获取） 方括号符号 []  取值
const currentSession = computed(() => {
  const session = moduleCurrentSession.value[currentModule.value] || null

  // 添加调试日志，帮助诊断问题
  if (sessionsLoaded.value) {
    console.log(`🔍 计算当前会话 [${currentModule.value}]:`, {
      sessionTitle: session?.title,
      messagesCount: session?.messages?.length || 0,
      sessionId: session?.id,
      sessionsLoaded: sessionsLoaded.value
    })
  }

  return session
})

// 判断当前模块是否需要会话面板（帮助与反馈不需要）
const needSessionPanel = computed(() => {
  return currentModule.value !== 'help'
})

// 获取当前模块的会话列表
const currentModuleSessions = computed(() => {
  return moduleSessions.value[currentModule.value] || []
})

// 获取排序后的当前模块会话列表（置顶会话在前）
const sortedCurrentModuleSessions = computed(() => {
  const sessions = [...currentModuleSessions.value]

  return sessions.sort((a, b) => {
    // 首先按置顶状态排序
    if (a.pinned && !b.pinned) return -1
    if (!a.pinned && b.pinned) return 1

    // 然后按更新时间排序（最新的在前）
    return new Date(b.updatedAt) - new Date(a.updatedAt)
  })
})

// 根据当前模块返回对应的组件
const currentComponent = computed(() => {
  const componentMap = {
    knowledge: KnowledgeCenter,
    business: BusinessDomain,
    function: FunctionDomain,
    help: HelpFeedback
  }
  return componentMap[currentModule.value] || KnowledgeCenter
})

// 获取当前会话的消息列表 - 增强版本，确保响应式更新
const currentMessages = computed(() => {
  // 如果数据还未加载完成，返回空数组但不影响后续更新
  if (!sessionsLoaded.value) {
    return []
  }

  const session = currentSession.value
  const messages = session?.messages || []

  // 添加调试日志
  console.log(`📨 计算当前消息列表 [${currentModule.value}]:`, {
    sessionTitle: session?.title,
    messagesCount: messages.length,
    sessionId: session?.id,
    hasMessages: messages.length > 0,
    sessionsLoaded: sessionsLoaded.value
  })

  return messages
})

/**
 * 显示通知
 * @param {string} message - 通知消息
 * @param {string} type - 通知类型 (info, success, warning, error)
 * @param {number} duration - 显示时长（毫秒）
 */
const showNotification = (message, type = 'info', duration = 3000) => {
  // 清除之前的通知定时器
  if (notificationTimer) {
    clearTimeout(notificationTimer)
  }

  // 设置通知内容
  notification.value = {
    show: true,
    message,
    type
  }

  // 设置自动隐藏
  notificationTimer = setTimeout(() => {
    notification.value.show = false
  }, duration)
}

/**
 * 初始化存储服务
 */
const initializeStorageService = async () => {
  console.log('初始化存储服务...')

  try {
    // 尝试初始化Redis存储服务
    const redisService = await getRedisSessionStorageService()
    const connectionManager = getRedisConnectionManager()

    // 主动检查Redis连接（不依赖缓存状态）
    console.log('主动检查Redis连接状态...')
    const isConnected = await redisService.checkRedisConnection(3)

    if (isConnected) {
      // Redis可用，使用Redis存储
      storageService.value = redisService
      redisConnectionManager.value = connectionManager
      useRedisStorage.value = true
      storageStatus.value = {
        type: 'redis',
        connected: true,
        error: null
      }

      console.log('✅ Redis存储服务初始化成功')
      showNotification('已连接到Redis存储服务', 'success')

      // 初始化会话同步服务
      const syncService = getSessionSyncService()
      sessionSyncService.value = syncService

      // 启用同步服务
      const syncEnabled = await syncService.enable()
      if (syncEnabled) {
        console.log('✅ 会话同步服务已启用')

        // 监听同步事件
        syncService.on('sessionUpdate', (data) => {
          console.log('收到会话更新同步:', data)
          // 这里可以添加处理逻辑，比如刷新当前会话数据
        })

        syncService.on('sessionDelete', (data) => {
          console.log('收到会话删除同步:', data)
          // 处理会话删除同步
        })

        syncService.on('syncError', (error) => {
          console.error('会话同步错误:', error)
        })
      }

      // 监听连接状态变化
      connectionManager.on('disconnected', () => {
        console.warn('Redis连接断开，切换到LocalStorage')
        storageStatus.value.connected = false
        showNotification('Redis连接断开，已切换到本地存储', 'warning')

        // 禁用同步服务
        if (sessionSyncService.value) {
          sessionSyncService.value.disable()
        }
      })

      connectionManager.on('connected', () => {
        console.log('Redis连接恢复')
        storageStatus.value.connected = true
        showNotification('Redis连接已恢复', 'success')

        // 重新启用同步服务
        if (sessionSyncService.value) {
          sessionSyncService.value.enable()
        }
      })

    } else {
      throw new Error('Redis连接不可用')
    }

  } catch (error) {
    console.warn('Redis存储服务初始化失败，使用LocalStorage:', error.message)

    // 回退到LocalStorage
    storageService.value = sessionStorageService
    useRedisStorage.value = false
    storageStatus.value = {
      type: 'localStorage',
      connected: true,
      error: error.message
    }

    showNotification('使用本地存储服务', 'info')
  }
}

/**
 * 自动保存会话数据
 */
const autoSaveSessionData = () => {
  if (!sessionsLoaded.value) {
    console.warn('会话未加载完成，跳过自动保存')
    return
  }

  // 清除之前的定时器
  if (autoSaveTimer) {
    clearTimeout(autoSaveTimer)
  }

  // 设置新的定时器，延迟保存以避免频繁操作
  autoSaveTimer = setTimeout(async () => {
    try {
      console.log('执行自动保存会话数据...')
      if (storageService.value) {
        // 检查Redis连接状态
        if (useRedisStorage.value && storageService.value.isSupported === false) {
          console.warn('Redis连接已断开，尝试重新连接...')
          await initializeStorageService()
        }

        const saved = await storageService.value.saveAllSessions(moduleSessions.value, moduleCurrentSession.value)
        if (saved) {
          console.log('✅ 自动保存成功')
        } else {
          console.error('❌ 自动保存失败')
        }
      } else {
        sessionStorageService.saveAllSessions(moduleSessions.value, moduleCurrentSession.value)
        console.log('✅ 自动保存到LocalStorage成功')
      }
    } catch (error) {
      console.error('❌ 自动保存过程出错:', error)
    }
  }, 1000) // 1秒延迟保存
}

/**
 * 加载会话数据
 */
const loadSessionData = async () => {
  console.log('开始加载会话数据...')

  try {
    // 使用动态存储服务加载数据
    let savedData = null
    if (storageService.value) {
      savedData = await storageService.value.loadAllSessions()
    } else {
      savedData = sessionStorageService.loadAllSessions()
    }

    if (savedData) {
      console.log('找到已保存的会话数据，正在恢复...')

      // 恢复会话数据
      moduleSessions.value = savedData.moduleSessions
      moduleCurrentSession.value = savedData.moduleCurrentSession

      // 为现有会话添加pinned属性和messages数组（兼容性处理）
      Object.values(moduleSessions.value).forEach(sessions => {
        sessions.forEach(session => {
          if (session.pinned === undefined) {
            session.pinned = false
          }
          // 确保每个会话都有messages数组
          if (!session.messages) {
            console.warn(`会话 "${session.title}" 缺少messages数组，正在初始化`)
            session.messages = []
          }
        })
      })

      console.log('会话数据恢复完成')

      // 验证所有模块的会话状态，确保刷新后每个模块都有正确的会话状态
      validateAllModulesSessionState()

      // 等待响应式系统完全更新
      await nextTick()

      // 强制触发响应式更新，确保组件能立即获取到正确的会话状态
      await forceReactiveUpdate()

      console.log('🔄 初始化完成，当前会话状态:', {
        module: currentModule.value,
        session: moduleCurrentSession.value[currentModule.value]?.title,
        messages: moduleCurrentSession.value[currentModule.value]?.messages?.length || 0
      })
      debugCurrentSessionState()
    } else {
      console.log('没有找到已保存的会话数据，将创建默认会话')
      // 没有保存的数据，创建默认会话
      handleNewSession(true) // 传递 true 表示自动创建

      // 确保其他模块在切换时也能正确初始化
      console.log('📝 首次使用，其他模块将在切换时自动创建会话')
    }
  } catch (error) {
    console.error('加载会话数据失败:', error)
    // 加载失败，创建默认会话
    handleNewSession(true) // 传递 true 表示自动创建
  } finally {
    // 强制触发计算属性重新计算
    await forceReactiveUpdate()

    // 等待所有响应式更新完成
    await nextTick()

    // 最终验证：如果当前模块有会话但消息列表为空，再次尝试修复
    await validateAndFixCurrentSession()

    // 再次等待，确保所有更新都已完成
    await nextTick()

    // 验证数据完整性后再标记为已加载
    const currentSessionData = moduleCurrentSession.value[currentModule.value]
    const currentMessagesData = currentSessionData?.messages || []

    console.log('📋 最终数据验证:', {
      module: currentModule.value,
      sessionTitle: currentSessionData?.title,
      messagesCount: currentMessagesData.length,
      computedMessagesCount: currentMessages.value.length
    })

    // 标记会话已加载
    sessionsLoaded.value = true

    console.log('✅ 会话数据加载完成，组件即将渲染')
    debugCurrentSessionState()
  }
}

/**
 * 验证所有模块的会话状态，确保刷新后每个模块都有正确的会话状态
 */
const validateAllModulesSessionState = () => {
  console.log('🔍 开始验证所有模块的会话状态...')

  // 支持会话的模块列表
  const sessionModules = ['knowledge', 'business', 'function']

  sessionModules.forEach(moduleKey => {
    const moduleSessionList = moduleSessions.value[moduleKey]
    const currentSessionForModule = moduleCurrentSession.value[moduleKey]
    const moduleName = getModuleDisplayName(moduleKey)

    console.log(`📋 检查${moduleName}模块: 会话数量=${moduleSessionList.length}, 当前会话=${currentSessionForModule?.title || 'null'}`)

    if (moduleSessionList.length > 0 && !currentSessionForModule) {
      // 有会话但没有当前会话，选择最新的会话
      const sortedSessions = [...moduleSessionList].sort((a, b) => {
        if (a.pinned && !b.pinned) return -1
        if (!a.pinned && b.pinned) return 1
        return new Date(b.updatedAt) - new Date(a.updatedAt)
      })

      const latestSession = sortedSessions[0]
      if (latestSession && latestSession.id) {
        moduleCurrentSession.value[moduleKey] = latestSession
        console.log(`✅ ${moduleName}已设置当前会话: "${latestSession.title}"`)
      }
    } else if (moduleSessionList.length === 0) {
      console.log(`📝 ${moduleName}暂无会话，将在切换时自动创建`)
    } else {
      console.log(`✅ ${moduleName}会话状态正常`)
    }
  })

  console.log('🔍 所有模块会话状态验证完成')
  autoSaveSessionData()

  // 强制触发响应式更新，确保计算属性能正确反映最新状态
  nextTick(() => {
    forceReactiveUpdate()
  })
}

/**
 * 确保会话数据完整性
 * @param {Object} session - 会话对象
 * @returns {Object} 修复后的会话对象
 */
const ensureSessionIntegrity = (session) => {
  if (!session) return session

  // 确保会话有messages数组
  if (!session.messages) {
    console.warn(`会话 "${session.title}" 缺少messages数组，正在初始化`)
    session.messages = []
  }

  // 确保会话有必要的属性
  if (!session.id) {
    session.id = Date.now().toString()
  }

  if (!session.createdAt) {
    session.createdAt = new Date()
  }

  if (!session.updatedAt) {
    session.updatedAt = new Date()
  }

  if (session.pinned === undefined) {
    session.pinned = false
  }

  return session
}

/**
 * 验证当前模块的会话状态，确保每个支持会话的模块都有活跃会话
 * @returns {boolean} 返回true表示模块已有会话，false表示新创建了会话
 */
const validateCurrentModuleSession = () => {
  const module = currentModule.value
  if (module === 'help') return true // 帮助模块不需要会话

  const moduleSessionList = moduleSessions.value[module]
  const currentSessionForModule = moduleCurrentSession.value[module]

  if (moduleSessionList.length === 0) {
    // 没有任何会话，自动创建新会话
    const moduleName = getModuleDisplayName(module)
    console.log(`📝 ${moduleName}暂无会话，正在为您创建新会话...`)
    handleNewSession(true) // 传递 true 表示自动创建
    return false // 表示新创建了会话
  } else if (!currentSessionForModule) {
    // 有会话列表但没有当前会话，选择最新的会话（排序后的第一个）
    const moduleName = getModuleDisplayName(module)
    console.log(`🔄 ${moduleName}检测到现有会话，正在为您切换到最新的会话...`)

    // 获取排序后的会话列表，选择最新的会话
    const sortedSessions = [...moduleSessionList].sort((a, b) => {
      // 首先按置顶状态排序
      if (a.pinned && !b.pinned) return -1
      if (!a.pinned && b.pinned) return 1
      // 然后按更新时间排序（最新的在前）
      return new Date(b.updatedAt) - new Date(a.updatedAt)
    })

    const latestSession = sortedSessions[0]
    if (latestSession && latestSession.id) {
      // 确保会话数据完整性
      ensureSessionIntegrity(latestSession)
      // 直接同步设置，不使用nextTick避免延迟
      moduleCurrentSession.value[module] = latestSession
      console.log(`✅ 已切换到最新会话: "${latestSession.title}"，消息数量: ${latestSession.messages?.length || 0}`)
      console.log(`📅 会话更新时间: ${new Date(latestSession.updatedAt).toLocaleString()}`)
      autoSaveSessionData()
    } else {
      console.warn(`⚠️ ${moduleName}的会话数据异常:`, latestSession)
      // 如果会话有问题，创建新会话
      handleNewSession(true)
      return false
    }
    return true // 表示使用了现有会话
  } else {
    // 验证当前会话是否还存在于会话列表中
    const sessionExists = moduleSessionList.some(s => s.id === currentSessionForModule.id)
    if (!sessionExists) {
      const moduleName = getModuleDisplayName(module)
      console.log(`⚠️ ${moduleName}的当前会话已不存在，正在切换到最近的会话...`)

      // 选择最新的会话（排序后的第一个）
      const sortedSessions = [...moduleSessionList].sort((a, b) => {
        // 首先按置顶状态排序
        if (a.pinned && !b.pinned) return -1
        if (!a.pinned && b.pinned) return 1
        // 然后按更新时间排序（最新的在前）
        return new Date(b.updatedAt) - new Date(a.updatedAt)
      })

      const latestSession = sortedSessions[0]
      if (latestSession && latestSession.id) {
        // 确保会话数据完整性
        ensureSessionIntegrity(latestSession)
        moduleCurrentSession.value[module] = latestSession
        console.log(`✅ 已恢复到最新会话: "${latestSession.title}"，消息数量: ${latestSession.messages?.length || 0}`)
        console.log(`📅 会话更新时间: ${new Date(latestSession.updatedAt).toLocaleString()}`)
        autoSaveSessionData()
      }
    }
    return true // 表示使用了现有会话
  }
}

/**
 * 处理模块切换
 * @param {string} module - 新选中的模块
 */
const handleModuleChange = async (module) => {
  console.log('切换模块:', module)
  const previousModule = currentModule.value
  currentModule.value = module

  // 强制重新渲染子组件，确保新模块的组件能正确显示
  componentKey.value++
  console.log(`🔄 模块切换，强制重新渲染，key: ${componentKey.value}`)

  // 如果切换到帮助与反馈模块，不需要会话管理
  if (module === 'help') {
    console.log('切换到帮助与反馈模块，无需会话管理')
    return
  }

  // 如果数据还未加载完成，等待加载完成
  if (!sessionsLoaded.value) {
    console.log('⏳ 数据还未加载完成，等待加载完成后再处理模块切换')
    return
  }

  // 验证切换后的模块会话状态，确保每个模块都有活跃会话
  const hadSession = validateCurrentModuleSession()

  // 如果是新创建的会话，给用户一个友好的提示
  if (!hadSession) {
    const moduleName = getModuleDisplayName(module)
    const message = `✨ 欢迎来到${moduleName}！已为您自动创建新会话`
    console.log(message)
    showNotification(message, 'success')
  }

  // 最终状态验证和日志
  const currentSessionInfo = moduleCurrentSession.value[module]
  console.log(`从 ${previousModule} 切换到 ${module}`)
  console.log(`当前会话:`, currentSessionInfo?.title)
  console.log(`消息数量:`, currentSessionInfo?.messages?.length || 0)

  // 确保当前会话状态正确
  if (currentSessionInfo) {
    console.log(`✅ ${getModuleDisplayName(module)}会话状态正常，可以开始聊天`)
    // 使用nextTick异步触发响应式更新和验证
    nextTick(async () => {
      await forceReactiveUpdate()
      await validateAndFixCurrentSession()
    })
  } else {
    console.warn(`⚠️ ${getModuleDisplayName(module)}会话状态异常，可能影响聊天功能`)
    debugCurrentSessionState()
    // 尝试强制刷新
    nextTick(async () => {
      await forceRefreshCurrentSession()
      await validateAndFixCurrentSession()
    })
  }
}

/**
 * 创建新会话
 * @param {boolean} isAutoCreated - 是否为自动创建（用于区分用户主动创建和系统自动创建）
 */
const handleNewSession = (isAutoCreated = false) => {
  // 检查登录状态（自动创建时跳过检查）
  if (!isAutoCreated && !requireLogin('创建新会话')) {
    console.warn('❌ 创建会话失败：用户未登录')
    return null
  }

  // 帮助与反馈模块不支持会话
  if (currentModule.value === 'help') {
    console.log('帮助与反馈模块不支持创建会话')
    return null
  }

  const moduleSessionCount = moduleSessions.value[currentModule.value].length
  const moduleName = getModuleDisplayName(currentModule.value)

  console.log(`🚀 开始${isAutoCreated ? '自动' : '手动'}创建${moduleName}会话...`)
  console.log(`📊 当前${moduleName}会话数量: ${moduleSessionCount}`)

  // 调试创建前状态
  debugSessionCreation('创建前')

  // 生成更友好的会话标题
  const sessionTitle = generateSessionTitle(currentModule.value, moduleSessionCount + 1, isAutoCreated)

  const newSession = {
    id: Date.now().toString(),
    title: sessionTitle,
    module: currentModule.value,
    messages: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    pinned: false // 新会话默认不置顶
  }

  console.log(`📝 创建会话对象:`, {
    id: newSession.id,
    title: newSession.title,
    module: newSession.module,
    messagesLength: newSession.messages.length
  })

  // 调试会话对象创建后状态
  debugSessionCreation('会话对象创建后', newSession)

  try {
    // 添加到对应模块的会话列表（添加到开头）
    const sessionList = moduleSessions.value[currentModule.value]
    sessionList.unshift(newSession)
    console.log(`✅ 会话已添加到${moduleName}列表，新的列表长度: ${sessionList.length}`)

    // 调试添加到列表后状态
    debugSessionCreation('添加到列表后', newSession)

    // 设置为当前模块的当前会话
    moduleCurrentSession.value[currentModule.value] = newSession
    console.log(`✅ 已设置为${moduleName}当前会话`)

    // 调试设置为当前会话后状态
    debugSessionCreation('设置为当前会话后', newSession)

    // 验证会话是否正确添加
    const verifySession = sessionList.find(s => s.id === newSession.id)
    if (!verifySession) {
      console.error('❌ 会话验证失败：会话未正确添加到列表')
      debugSessionCreation('验证失败', newSession)
      return null
    }

    const actionType = isAutoCreated ? '自动创建' : '手动创建'
    console.log(`✨ ${actionType}${moduleName}新会话成功: "${newSession.title}" (ID: ${newSession.id})`)

    // 同步会话创建
    if (sessionSyncService.value) {
      sessionSyncService.value.syncSessionCreate(currentModule.value, newSession)
      console.log('📡 会话创建已同步')
    }

    // 自动保存
    autoSaveSessionData()
    console.log('💾 会话创建后自动保存已触发')

    // 调试最终状态
    debugSessionCreation('创建完成', newSession)

    return newSession
  } catch (error) {
    console.error('❌ 创建会话过程中发生错误:', error)
    return null
  }
}

/**
 * 生成会话标题
 * @param {string} moduleKey - 模块键值
 * @param {number} sessionNumber - 会话编号
 * @param {boolean} isAutoCreated - 是否为自动创建
 * @returns {string} 会话标题
 */
const generateSessionTitle = (moduleKey, sessionNumber, isAutoCreated = false) => {
  const moduleName = getModuleDisplayName(moduleKey)
  const now = new Date()
  const timeStr = now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })

  if (isAutoCreated) {
    // 自动创建的会话使用时间标识
    return `${moduleName}会话 ${timeStr}`
  } else {
    // 用户手动创建的会话使用编号
    return `${moduleName}会话 ${sessionNumber}`
  }
}

/**
 * 调试当前会话状态
 */
const debugCurrentSessionState = () => {
  const module = currentModule.value
  console.log('🔍 调试会话状态:', {
    currentModule: module,
    moduleSessions: moduleSessions.value[module],
    moduleCurrentSession: moduleCurrentSession.value[module],
    currentSession: currentSession.value,
    currentMessages: currentMessages.value,
    sessionsLoaded: sessionsLoaded.value
  })
}

/**
 * 调试会话创建状态
 * @param {string} stage - 调试阶段
 * @param {Object} sessionData - 会话数据
 */
const debugSessionCreation = (stage, sessionData = null) => {
  const moduleName = getModuleDisplayName(currentModule.value)
  console.log(`🔧 [${stage}] ${moduleName}会话创建调试:`)

  if (sessionData) {
    console.log('  会话数据:', {
      id: sessionData.id,
      title: sessionData.title,
      module: sessionData.module,
      messagesCount: sessionData.messages?.length || 0
    })
  }

  console.log('  当前模块:', currentModule.value)
  console.log('  会话列表长度:', moduleSessions.value[currentModule.value]?.length || 0)
  console.log('  当前会话:', moduleCurrentSession.value[currentModule.value]?.title || 'null')
  console.log('  会话已加载:', sessionsLoaded.value)

  // 检查会话列表中是否包含指定会话
  if (sessionData) {
    const inList = moduleSessions.value[currentModule.value]?.find(s => s.id === sessionData.id)
    console.log('  会话在列表中:', !!inList)
    if (inList) {
      const index = moduleSessions.value[currentModule.value].findIndex(s => s.id === sessionData.id)
      console.log('  会话在列表中的位置:', index)
    }
  }

  console.log('  计算属性会话列表长度:', currentModuleSessions.value.length)
  console.log('  排序后会话列表长度:', sortedCurrentModuleSessions.value.length)
}

/**
 * 验证并修复当前会话状态
 * 确保页面刷新后会话内容能正确显示
 */
const validateAndFixCurrentSession = async () => {
  const module = currentModule.value
  if (module === 'help') return

  const session = moduleCurrentSession.value[module]
  const messages = currentMessages.value

  console.log('🔧 验证当前会话状态:', {
    module,
    sessionTitle: session?.title,
    sessionMessages: session?.messages?.length || 0,
    computedMessages: messages.length
  })

  // 如果会话存在但计算属性返回的消息为空，说明响应式更新有问题
  if (session && session.messages && session.messages.length > 0 && messages.length === 0) {
    console.warn('⚠️ 检测到会话数据不同步，尝试修复...')

    // 强制重新设置当前会话
    const sessionCopy = { ...session }
    moduleCurrentSession.value[module] = sessionCopy

    // 等待响应式更新
    await nextTick()

    // 再次检查
    const updatedMessages = currentMessages.value
    console.log('🔧 修复后消息数量:', updatedMessages.length)

    if (updatedMessages.length === 0) {
      // 如果还是没有消息，强制重新渲染
      componentKey.value++
      await nextTick()
      console.log('🔧 强制重新渲染后消息数量:', currentMessages.value.length)
    }
  }
}

/**
 * 强制触发响应式更新
 * 解决刷新后计算属性不更新的问题
 */
const forceReactiveUpdate = async () => {
  console.log('🔄 强制触发响应式更新...')

  // 1. 强制触发 moduleCurrentSession 的响应式更新
  const currentModuleCurrentSession = moduleCurrentSession.value
  moduleCurrentSession.value = { ...currentModuleCurrentSession }

  // 2. 强制触发 moduleSessions 的响应式更新
  const currentModuleSessions = moduleSessions.value
  moduleSessions.value = { ...currentModuleSessions }

  // 等待响应式系统处理
  await nextTick()

  // 3. 强制重新渲染子组件
  componentKey.value++
  console.log(`🔄 强制重新渲染子组件，key: ${componentKey.value}`)

  // 再次等待渲染完成
  await nextTick()

  // 验证更新结果
  const module = currentModule.value
  const session = moduleCurrentSession.value[module]
  const messages = session?.messages || []

  console.log('🔄 响应式更新完成:', {
    module,
    sessionTitle: session?.title,
    messagesCount: messages.length,
    currentSession: currentSession.value?.title,
    currentMessagesCount: currentMessages.value.length,
    componentKey: componentKey.value
  })

  // 如果消息数量不匹配，说明还有问题，再次尝试
  if (session && session.messages && session.messages.length !== currentMessages.value.length) {
    console.warn('⚠️ 消息数量不匹配，再次尝试强制更新')
    await nextTick()
    componentKey.value++
  }
}

/**
 * 强制刷新当前会话状态
 * 用于解决会话切换后状态不同步的问题
 */
const forceRefreshCurrentSession = async () => {
  const module = currentModule.value
  if (module === 'help') return

  const currentSessionRef = moduleCurrentSession.value[module]
  if (currentSessionRef) {
    // 强制触发响应式更新
    await forceReactiveUpdate()
    console.log(`🔄 强制刷新${getModuleDisplayName(module)}会话状态`)
  }
}

/**
 * 获取模块显示名称
 * @param {string} moduleKey - 模块键值
 * @returns {string} 模块显示名称
 */
const getModuleDisplayName = (moduleKey) => {
  const moduleNames = {
    knowledge: '知识中心',
    business: '业务域',
    function: '职能域',
    help: '帮助反馈'
  }
  return moduleNames[moduleKey] || '未知模块'
}

/**
 * 切换会话
 * @param {Object} session - 要切换到的会话
 */
const handleSessionChange = (session) => {
  // 检查登录状态
  if (!requireLogin('切换会话')) {
    return
  }

  if (!session || !session.id) {
    console.warn('尝试切换到无效的会话:', session)
    return
  }

  // 验证会话是否属于当前模块
  if (session.module !== currentModule.value) {
    console.warn('会话模块不匹配:', session.module, '当前模块:', currentModule.value)
    return
  }

  // 验证会话是否存在于当前模块的会话列表中
  const moduleSessionList = moduleSessions.value[currentModule.value]
  const sessionExists = moduleSessionList.some(s => s.id === session.id)

  if (!sessionExists) {
    console.warn('会话不存在于当前模块的会话列表中:', session.id)
    return
  }

  console.log('切换会话:', session.title)

  // 确保会话数据完整性
  ensureSessionIntegrity(session)
  // 设置为当前模块的当前会话
  moduleCurrentSession.value[currentModule.value] = session

  console.log(`在${getModuleDisplayName(currentModule.value)}中切换到会话:`, session.title)

  // 同步会话切换
  if (sessionSyncService.value) {
    sessionSyncService.value.syncSessionSwitch(currentModule.value, session)
  }

  // 自动保存
  autoSaveSessionData()
}

/**
 * 删除会话
 * @param {Object} session - 要删除的会话
 */
const handleDeleteSession = (session) => {
  // 检查登录状态
  if (!requireLogin('删除会话')) {
    return
  }

  const moduleKey = session.module
  const moduleSessionList = moduleSessions.value[moduleKey]
  const index = moduleSessionList.findIndex(s => s.id === session.id)

  if (index === -1) {
    console.warn('要删除的会话不存在:', session.id)
    return
  }

  console.log(`准备删除会话: ${session.title} (模块: ${getModuleDisplayName(moduleKey)})`)

  // 从对应模块的会话列表中移除
  moduleSessionList.splice(index, 1)

  // 如果删除的是当前模块的当前会话，需要处理当前会话的切换
  const isCurrentSession = moduleCurrentSession.value[moduleKey]?.id === session.id

  if (isCurrentSession) {
    if (moduleSessionList.length > 0) {
      // 切换到最近的会话（第一个）
      moduleCurrentSession.value[moduleKey] = moduleSessionList[0]
      console.log(`切换到会话: ${moduleSessionList[0].title}`)
    } else {
      // 没有其他会话，如果是当前模块，自动创建新会话
      if (moduleKey === currentModule.value) {
        console.log('🔄 当前模块没有会话了，正在为您自动创建新会话...')
        // 先清空当前会话
        moduleCurrentSession.value[moduleKey] = null
        // 创建新会话（自动创建）
        handleNewSession(true)
        return // handleNewSession 会自动保存，这里直接返回
      } else {
        // 非当前模块，只清空当前会话
        moduleCurrentSession.value[moduleKey] = null
      }
    }
  }

  console.log(`成功删除会话: ${session.title}`)

  // 同步会话删除
  if (sessionSyncService.value) {
    sessionSyncService.value.syncSessionDelete(moduleKey, session.id)
  }

  // 自动保存
  autoSaveSessionData()
}

/**
 * 重命名会话
 * @param {Object} session - 要重命名的会话
 * @param {string} newTitle - 新的标题
 */
const handleRenameSession = (session, newTitle) => {
  // 检查登录状态
  if (!requireLogin('重命名会话')) {
    return
  }

  const moduleKey = session.module
  const moduleSessionList = moduleSessions.value[moduleKey]
  const sessionIndex = moduleSessionList.findIndex(s => s.id === session.id)

  if (sessionIndex === -1) {
    console.warn('要重命名的会话不存在:', session.id)
    return
  }

  // 更新会话标题
  moduleSessionList[sessionIndex].title = newTitle
  moduleSessionList[sessionIndex].updatedAt = new Date()

  console.log(`重命名会话: "${session.title}" -> "${newTitle}"`)

  // 自动保存
  autoSaveSessionData()
}

/**
 * 切换会话置顶状态
 * @param {Object} session - 要切换置顶状态的会话
 */
const handleTogglePin = (session) => {
  const moduleKey = session.module
  const moduleSessionList = moduleSessions.value[moduleKey]
  const sessionIndex = moduleSessionList.findIndex(s => s.id === session.id)

  if (sessionIndex === -1) {
    console.warn('要置顶的会话不存在:', session.id)
    return
  }

  // 切换置顶状态
  const newPinnedState = !moduleSessionList[sessionIndex].pinned
  moduleSessionList[sessionIndex].pinned = newPinnedState
  moduleSessionList[sessionIndex].updatedAt = new Date()

  const action = newPinnedState ? '置顶' : '取消置顶'
  console.log(`${action}会话: "${session.title}"`)

  // 自动保存
  autoSaveSessionData()
}

/**
 * 发送消息
 * @param {string} content - 消息内容
 * @param {Object} options - 额外选项（如选择的库或功能）
 */
const handleSendMessage = async (content, options = {}) => {
  // 检查登录状态，但在测试模式下更宽松
  if (!isUserLoggedIn.value) {
    console.warn('⚠️ 用户未登录，但继续执行以测试会话创建功能')
    // 在生产环境中应该取消注释下面的代码
    // if (!requireLogin('发送消息')) {
    //   return
    // }
  } else {
    console.log('✅ 用户已登录，继续发送消息')
  }

  // 帮助与反馈模块不支持发送消息
  if (currentModule.value === 'help') {
    console.log('帮助与反馈模块不支持发送消息')
    return
  }

  // 检查内容是否为空
  if (!content.trim()) return

  // 获取当前会话，如果不存在则自动创建
  let session = moduleCurrentSession.value[currentModule.value]
  if (!session) {
    console.log(`当前模块${currentModule.value}没有会话，自动创建新会话`)

    // 记录创建前的会话数量
    const beforeCount = moduleSessions.value[currentModule.value].length
    console.log(`📊 创建前${getModuleDisplayName(currentModule.value)}会话数量: ${beforeCount}`)

    // 自动创建会话并获取返回值
    const createdSession = handleNewSession(true)

    // 检查会话创建是否成功
    if (!createdSession) {
      console.error('❌ 自动创建会话失败 - handleNewSession返回null')
      showNotification('创建会话失败，请重试', 'error')
      return
    }

    // 等待响应式更新完成
    await nextTick()
    session = moduleCurrentSession.value[currentModule.value]

    // 验证会话创建结果
    const afterCount = moduleSessions.value[currentModule.value].length
    console.log(`📊 创建后${getModuleDisplayName(currentModule.value)}会话数量: ${afterCount}`)

    // 双重验证：检查当前会话和创建的会话是否一致
    if (!session || session.id !== createdSession.id) {
      console.error('❌ 会话创建异常 - 当前会话与创建的会话不一致')
      console.log('当前会话:', session)
      console.log('创建的会话:', createdSession)

      // 尝试修复：直接设置为创建的会话
      console.log('🔧 尝试修复会话状态...')
      moduleCurrentSession.value[currentModule.value] = createdSession
      session = createdSession

      // 强制触发响应式更新
      await forceReactiveUpdate()
    }

    // 验证会话是否正确添加到会话列表
    const sessionInList = moduleSessions.value[currentModule.value].find(s => s.id === session.id)
    if (!sessionInList) {
      console.error('❌ 会话创建异常 - 会话未添加到列表中')
      console.log('当前会话:', session)
      console.log('会话列表:', moduleSessions.value[currentModule.value])

      // 尝试手动添加到列表
      console.log('🔧 尝试手动修复会话列表...')
      moduleSessions.value[currentModule.value].unshift(session)

      // 强制触发响应式更新
      await forceReactiveUpdate()
    }

    console.log(`✅ 会话创建验证成功: "${session.title}" (ID: ${session.id})`)
    console.log(`📋 会话列表中的会话数量: ${moduleSessions.value[currentModule.value].length}`)
    console.log(`🔍 会话在列表中的位置: ${moduleSessions.value[currentModule.value].findIndex(s => s.id === session.id)}`)

    // 调试最终验证状态
    debugSessionCreation('发送消息前最终验证', session)

    // 立即保存新创建的会话，确保不会因为延迟保存而丢失
    console.log('💾 立即保存新创建的会话到存储')
    try {
      if (storageService.value) {
        await storageService.value.saveAllSessions(moduleSessions.value, moduleCurrentSession.value)
      } else {
        sessionStorageService.saveAllSessions(moduleSessions.value, moduleCurrentSession.value)
      }
      console.log('✅ 新会话已保存到存储')
    } catch (error) {
      console.error('❌ 保存新会话失败:', error)
    }
  }

  // 确保会话有messages数组
  if (!session.messages) {
    console.warn('会话缺少messages数组，正在初始化')
    session.messages = []
  }

  // 创建用户消息
  const userMessage = {
    id: Date.now().toString(),
    type: 'user',
    content: content.trim(),
    timestamp: new Date(),
    ...options
  }

  // 添加到当前会话
  session.messages.push(userMessage)
  session.updatedAt = new Date()

  const moduleName = getModuleDisplayName(currentModule.value)
  console.log(`在${moduleName}中发送消息:`, userMessage)

  // 立即保存用户消息，确保不会丢失
  try {
    if (storageService.value) {
      await storageService.value.saveAllSessions(moduleSessions.value, moduleCurrentSession.value)
    } else {
      sessionStorageService.saveAllSessions(moduleSessions.value, moduleCurrentSession.value)
    }
    console.log('✅ 用户消息已保存到存储')
  } catch (error) {
    console.error('❌ 保存用户消息失败:', error)
  }

  // 创建AI流式消息
  const streamingMessage = {
    id: (Date.now() + 1).toString(),
    type: 'ai',
    content: '',
    timestamp: new Date(),
    loading: true,
    streaming: false,
    streamingContent: '',
    module: currentModule.value // 添加模块标识，确保AIMessage组件能获取正确的AI配置
  }

  session.messages.push(streamingMessage)

  try {
    // 获取AI服务实例
    const aiService = getAIService(currentModule.value)

    // 更新为流式状态
    const messageIndex = session.messages.findIndex(msg => msg.id === streamingMessage.id)
    if (messageIndex !== -1) {
      session.messages[messageIndex] = {
        ...streamingMessage,
        loading: false,
        streaming: true
      }
    }

    // 调用AI服务获取流式回复
    // 所有模块都不显示推理过程，直接流式输出AI回复内容
    const shouldShowReasoning = false

    const aiReply = await aiService.sendMessageStream(
      session.messages.filter(msg => !msg.loading && !msg.streaming),
      (chunk, fullContent) => {
        // 流式更新回调
        const msgIndex = session.messages.findIndex(msg => msg.id === streamingMessage.id)
        if (msgIndex !== -1) {
          session.messages[msgIndex].streamingContent = fullContent
        }
      },
      shouldShowReasoning ? (reasoningData) => {
        // 推理过程回调（已禁用）
        const msgIndex = session.messages.findIndex(msg => msg.id === streamingMessage.id)
        if (msgIndex !== -1) {
          const message = session.messages[msgIndex]

          if (reasoningData.type === 'step_start') {
            // 初始化推理步骤数组
            if (!message.reasoningSteps) {
              message.reasoningSteps = []
            }

            // 添加新步骤
            message.reasoningSteps[reasoningData.stepIndex] = reasoningData.step
            message.currentReasoningStep = reasoningData.stepIndex
            message.reasoningActive = true

          } else if (reasoningData.type === 'step_complete') {
            // 更新步骤状态
            if (message.reasoningSteps && message.reasoningSteps[reasoningData.stepIndex]) {
              message.reasoningSteps[reasoningData.stepIndex] = reasoningData.step
            }

          } else if (reasoningData.type === 'reasoning_complete') {
            // 推理完成
            message.reasoningActive = false
            message.currentReasoningStep = -1
          }
        }
      } : null // 所有模块都不传递推理过程回调
    )

    // 流式完成，更新为最终状态
    const finalIndex = session.messages.findIndex(msg => msg.id === streamingMessage.id)
    if (finalIndex !== -1) {
      // 确保AI回复内容是字符串类型
      let safeAiReply = aiReply
      if (typeof aiReply === 'object') {
        console.warn('AI回复是对象类型，转换为字符串:', aiReply)
        try {
          safeAiReply = JSON.stringify(aiReply, null, 2)
        } catch (error) {
          console.error('AI回复对象转换失败:', error)
          safeAiReply = '回复内容格式错误'
        }
      } else {
        safeAiReply = String(aiReply || '')
      }

      // 解析思维链内容（已禁用）
      let finalContent = safeAiReply
      let extractedReasoning = null

      // 所有模块都直接使用完整内容，不进行思维链解析
      if (shouldShowReasoning) {
        try {
          const aiService = getAIService(currentModule.value)
          const parsed = aiService.parseThinkingContent(safeAiReply)
          finalContent = parsed.finalAnswer
          extractedReasoning = parsed.thinking

          if (extractedReasoning) {
            console.log('提取到推理过程:', extractedReasoning.substring(0, 100))
          }
        } catch (error) {
          console.error('解析思维链失败:', error)
          finalContent = safeAiReply
        }
      } else {
        // 所有模块都直接使用完整的AI回复内容，不进行思维链解析
        console.log(`[${currentModule.value}] 跳过思维链解析，直接使用完整内容`)
        finalContent = safeAiReply
      }

      session.messages[finalIndex] = {
        ...streamingMessage,
        content: finalContent,
        loading: false,
        streaming: false,
        streamingContent: '',
        // 使用提取的推理过程或保留动态推理步骤
        reasoning: extractedReasoning ? [{
          title: '思维过程',
          description: extractedReasoning,
          type: 'thinking_content'
        }] : (streamingMessage.reasoningSteps || []),
        reasoningActive: false,
        currentReasoningStep: -1,
        // 保存原始推理文本
        thinkingContent: extractedReasoning,
        // 确保模块标识被保留
        module: currentModule.value
      }
    }

    session.updatedAt = new Date()
    console.log(`在${moduleName}中收到AI流式回复完成`)

    // 立即保存AI回复，确保不会丢失
    console.log('立即保存AI回复到存储')
    try {
      if (storageService.value) {
        await storageService.value.saveAllSessions(moduleSessions.value, moduleCurrentSession.value)
      } else {
        sessionStorageService.saveAllSessions(moduleSessions.value, moduleCurrentSession.value)
      }
      console.log('✅ AI回复已保存到存储')
    } catch (error) {
      console.error('❌ 保存AI回复失败:', error)
      // 如果立即保存失败，仍然触发延迟保存作为备份
      autoSaveSessionData()
    }

  } catch (error) {
    console.error(`在${moduleName}中AI流式回复失败:`, error)

    // 更新消息为错误状态
    const errorIndex = session.messages.findIndex(msg => msg.id === streamingMessage.id)
    if (errorIndex !== -1) {
      // 确保错误消息也是字符串类型
      const errorMessage = `抱歉，AI服务暂时不可用：${String(error.message || '未知错误')}`

      session.messages[errorIndex] = {
        ...streamingMessage,
        content: errorMessage,
        loading: false,
        streaming: false,
        error: true,
        // 确保模块标识被保留
        module: currentModule.value
      }
    }

    // 即使出错也要保存状态
    autoSaveSessionData()
  }
}

/**
 * 清空当前会话的消息
 */
const handleClearMessages = () => {
  const session = moduleCurrentSession.value[currentModule.value]
  if (!session) {
    console.warn('没有找到当前会话，无法清空消息')
    return
  }

  // 清空当前会话的消息
  session.messages = []
  session.updatedAt = new Date()

  const moduleName = getModuleDisplayName(currentModule.value)
  console.log(`已清空${moduleName}当前会话的所有消息`)

  // 自动保存
  autoSaveSessionData()
}

/**
 * 处理Redis重连
 */
const handleRedisReconnect = async () => {
  try {
    console.log('尝试重新连接Redis...')
    showNotification('正在重新连接Redis...', 'info')

    if (redisConnectionManager.value) {
      const connected = await redisConnectionManager.value.reconnect()

      if (connected) {
        storageStatus.value.connected = true
        showNotification('Redis重连成功', 'success')

        // 重连成功后重新初始化存储服务
        await initializeStorageService()
      } else {
        showNotification('Redis重连失败，请检查Redis服务器状态', 'error')
      }
    } else {
      // 如果连接管理器不存在，尝试重新初始化整个存储服务
      await initializeStorageService()
    }
  } catch (error) {
    console.error('Redis重连过程出错:', error)
    showNotification(`Redis重连失败: ${error.message}`, 'error')
  }
}

/**
 * 测试Redis连接
 */
const testRedisConnection = async () => {
  try {
    console.log('测试Redis连接...')
    showNotification('正在测试Redis连接...', 'info')

    // 尝试获取Redis适配器并测试连接
    const { getRedisAdapter } = await import('../../services/redisHttpAdapter.js')
    const redisAdapter = getRedisAdapter()

    const connected = await redisAdapter.ping()

    if (connected) {
      showNotification('Redis连接测试成功', 'success')
      console.log('✅ Redis连接正常')
      return true
    } else {
      showNotification('Redis连接测试失败', 'error')
      console.log('❌ Redis连接失败')
      return false
    }
  } catch (error) {
    console.error('Redis连接测试出错:', error)
    showNotification(`Redis连接测试失败: ${error.message}`, 'error')
    return false
  }
}

// 暴露测试函数到全局，方便调试
if (typeof window !== 'undefined') {
  window.testRedisConnection = testRedisConnection
  window.debugSessionState = () => {
    console.log('当前会话状态:', {
      moduleSessions: moduleSessions.value,
      moduleCurrentSession: moduleCurrentSession.value,
      storageStatus: storageStatus.value,
      useRedisStorage: useRedisStorage.value
    })
  }
  window.forceRefreshSession = async () => {
    console.log('强制刷新会话数据...')
    await loadSessionData()
    showNotification('会话数据已刷新', 'info')
  }
  window.runRedisDiagnostics = async () => {
    try {
      const { getRedisDiagnostics } = await import('../../utils/redisDiagnostics.js')
      const diagnostics = getRedisDiagnostics()
      const result = await diagnostics.runFullDiagnostics()

      if (result.success) {
        showNotification('Redis诊断完成，所有检查通过', 'success')
      } else {
        showNotification('Redis诊断发现问题，请查看控制台', 'warning')
      }

      return result
    } catch (error) {
      console.error('运行Redis诊断失败:', error)
      showNotification('Redis诊断失败', 'error')
      return null
    }
  }
}

/**
 * 处理数据迁移
 */
const handleDataMigration = async () => {
  try {
    console.log('开始数据迁移...')
    showNotification('正在检查迁移条件...', 'info')

    const migrationTool = getSessionMigrationTool()

    // 检查迁移前置条件
    const prerequisites = await migrationTool.checkMigrationPrerequisites()

    console.log('迁移前置条件检查结果:', prerequisites)

    // 显示详细的检查结果
    if (prerequisites.details && prerequisites.details.length > 0) {
      console.log('检查详情:', prerequisites.details.join('\n'))
    }

    if (!prerequisites.canProceed) {
      const errorMessage = prerequisites.details ?
        prerequisites.details.join('\n') :
        '迁移前置条件不满足'

      // 显示详细错误信息
      showNotification('迁移条件检查失败，请查看控制台了解详情', 'error')
      console.error('迁移前置条件不满足:', errorMessage)

      // 如果是Redis连接问题，提供重连建议
      if (!prerequisites.redisConnected) {
        showNotification('Redis连接失败，请检查Redis服务器状态或尝试重新连接', 'warning')
      }

      return
    }

    // 显示警告信息（如果有）
    if (prerequisites.warnings && prerequisites.warnings.length > 0) {
      prerequisites.warnings.forEach(warning => {
        console.warn('迁移警告:', warning)
      })
    }

    showNotification('开始执行数据迁移...', 'info')

    // 执行迁移
    const result = await migrationTool.performMigration({
      createBackup: true,
      clearLocalAfterMigration: false,
      validateAfterMigration: true
    })

    if (result.success) {
      // 迁移成功，重新初始化存储服务
      await initializeStorageService()

      if (result.migratedSessions > 0) {
        showNotification(`数据迁移成功！已迁移${result.migratedSessions}个会话`, 'success')
      } else {
        showNotification('Redis存储初始化成功！', 'success')
      }
    } else {
      throw new Error(result.error || '迁移失败')
    }
  } catch (error) {
    console.error('数据迁移失败:', error)

    // 提供更友好的错误信息
    let errorMessage = error.message
    if (errorMessage.includes('Redis')) {
      errorMessage = 'Redis连接或操作失败，请检查Redis服务器状态'
    } else if (errorMessage.includes('LocalStorage')) {
      errorMessage = 'LocalStorage访问失败，请检查浏览器权限'
    }

    showNotification(`数据迁移失败: ${errorMessage}`, 'error')
  }
}

/**
 * 检查用户登录状态
 */
const checkUserLoginStatus = async () => {
  try {
    const currentUser = await userService.getCurrentUser()
    isUserLoggedIn.value = !!currentUser

    if (currentUser) {
      console.log('检测到已登录用户:', currentUser)
      return true
    } else {
      console.log('用户未登录')
      return false
    }
  } catch (error) {
    console.error('检查用户登录状态失败:', error)
    isUserLoggedIn.value = false
    return false
  }
}

/**
 * 登录状态守卫 - 检查用户是否已登录
 * @param {string} operation - 操作名称（用于日志）
 * @returns {boolean} 是否允许继续操作
 */
const requireLogin = (operation = '操作') => {
  if (!isUserLoggedIn.value) {
    console.warn(`${operation}需要用户登录`)
    showNotification('请先登录后再进行此操作', 'warning')
    return false
  }
  return true
}

/**
 * 处理用户登录成功
 * @param {Object} userData - 用户数据
 */
const handleUserLogin = async (userData) => {
  console.log('用户登录成功，开始初始化Redis连接和会话数据:', userData)

  try {
    // 更新登录状态
    isUserLoggedIn.value = true

    // 用户登录完成时，立即尝试连接Redis
    console.log('🔗 用户登录完成，正在建立Redis连接...')
    await initializeRedisConnectionAfterLogin()

    // 用户登录后，重新加载会话数据（会自动使用用户专用存储）
    await loadSessionData()

    // 刷新所有动态水印
    try {
      await refreshAllDynamicWatermarks()
      console.log('✅ 用户登录后水印已更新')
    } catch (error) {
      console.error('❌ 刷新水印失败:', error)
    }

    showNotification(`欢迎 ${userData.username}！`, 'success')
  } catch (error) {
    console.error('用户登录后初始化失败:', error)
    showNotification('用户登录后初始化失败', 'error')
  }
}

/**
 * 用户登录后初始化Redis连接
 * 这是用户登录完成时的专用Redis连接时间点
 */
const initializeRedisConnectionAfterLogin = async () => {
  try {
    console.log('🚀 开始用户登录后的Redis连接初始化...')

    // 显示连接状态通知
    showNotification('正在建立Redis连接...', 'info', 2000)

    // 重新初始化存储服务，这会触发Redis连接
    await initializeStorageService()

    // 如果Redis连接成功，进行额外的登录后配置
    if (useRedisStorage.value && storageStatus.value.connected) {
      console.log('✅ 用户登录后Redis连接成功，进行登录后配置...')

      // 可以在这里添加用户登录后的特殊Redis操作
      await performPostLoginRedisOperations()

      showNotification('Redis连接已建立', 'success', 2000)
    } else {
      console.log('⚠️ Redis连接失败，将使用本地存储')
      showNotification('Redis连接失败，使用本地存储', 'warning', 3000)
    }
  } catch (error) {
    console.error('用户登录后Redis连接初始化失败:', error)
    showNotification('Redis连接初始化失败', 'error', 3000)
  }
}

/**
 * 执行用户登录后的Redis操作
 * 可以在这里添加用户登录后需要执行的特殊Redis操作
 */
const performPostLoginRedisOperations = async () => {
  try {
    console.log('🔧 执行用户登录后的Redis配置操作...')

    // 获取当前用户信息
    const currentUser = await userService.getCurrentUser()
    if (!currentUser) {
      console.warn('无法获取当前用户信息，跳过登录后Redis操作')
      return
    }

    // 记录用户登录时间到Redis（可选）
    if (storageService.value && typeof storageService.value.redis !== 'undefined') {
      const loginKey = `user:login:${currentUser.userKey}`
      const loginData = {
        username: currentUser.username,
        employeeId: currentUser.employeeId,
        loginTime: new Date().toISOString(),
        sessionId: Date.now().toString()
      }

      // 设置登录记录，过期时间为24小时
      await storageService.value.redis.set(loginKey, JSON.stringify(loginData), 24 * 60 * 60)
      console.log('✅ 用户登录记录已保存到Redis')
    }

    // 初始化用户专用的Redis键空间（如果需要）
    console.log('✅ 用户登录后Redis操作完成')
  } catch (error) {
    console.error('执行用户登录后Redis操作失败:', error)
    // 这里的错误不应该影响主要的登录流程
  }
}

/**
 * 处理用户退出登录
 */
const handleUserLogout = async () => {
  console.log('用户退出登录，清理会话数据')

  try {
    // 更新登录状态
    isUserLoggedIn.value = false

    // 清空当前会话数据
    moduleSessions.value = {
      knowledge: [],
      business: [],
      function: []
    }
    moduleCurrentSession.value = {
      knowledge: null,
      business: null,
      function: null
    }

    // 标记为未加载状态
    sessionsLoaded.value = false

    // 刷新所有动态水印（显示未登录状态）
    try {
      await refreshAllDynamicWatermarks()
      console.log('✅ 用户退出后水印已更新为未登录状态')
    } catch (error) {
      console.error('❌ 刷新水印失败:', error)
    }

    showNotification('已退出登录', 'info')

    // 注意：不重新加载会话数据，因为用户需要重新登录
    console.log('用户已退出，等待重新登录')
  } catch (error) {
    console.error('用户退出登录处理失败:', error)
    showNotification('退出登录处理失败', 'error')
  }
}

/**
 * 显示存储信息
 */
const showStorageInfo = async () => {
  try {
    let info = null
    if (storageService.value && typeof storageService.value.getStorageInfo === 'function') {
      info = await storageService.value.getStorageInfo()
    } else {
      info = sessionStorageService.getStorageInfo()
    }

    console.log('存储信息:', info)

    // 创建信息显示对话框
    const infoText = JSON.stringify(info, null, 2)
    const blob = new Blob([infoText], { type: 'application/json' })
    const url = URL.createObjectURL(blob)

    // 在新窗口中显示信息
    const newWindow = window.open('', '_blank', 'width=600,height=400')
    if (newWindow) {
      newWindow.document.write(`
        <html>
          <head><title>存储服务信息</title></head>
          <body>
            <h2>存储服务信息</h2>
            <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; overflow: auto;">${infoText}</pre>
          </body>
        </html>
      `)
    }

    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('获取存储信息失败:', error)
    showNotification('获取存储信息失败', 'error')
  }
}

/**
 * 切换会话面板折叠状态
 */
const handleToggleSessionPanel = () => {
  isSessionPanelCollapsed.value = !isSessionPanelCollapsed.value
  console.log('切换会话面板折叠状态:', isSessionPanelCollapsed.value ? '折叠' : '展开')
}

/**
 * 处理会话面板触摸开始（移动端滑动支持）
 */
const handleSessionPanelTouchStart = (event) => {
  if (!isMobile.value) return
  touchGestures.handleTouchStart(event)
}

/**
 * 处理会话面板触摸移动
 */
const handleSessionPanelTouchMove = (event) => {
  if (!isMobile.value) return
  touchGestures.handleTouchMove(event)
}

/**
 * 处理会话面板触摸结束
 */
const handleSessionPanelTouchEnd = (event) => {
  if (!isMobile.value) return

  touchGestures.handleTouchEnd(event)

  // 检查是否为左滑手势（折叠面板）
  if (touchGestures.isSwipeLeft() && !isSessionPanelCollapsed.value) {
    isSessionPanelCollapsed.value = true
    console.log('左滑手势：折叠会话面板')
  }

  // 检查是否为右滑手势（展开面板）
  if (touchGestures.isSwipeRight() && isSessionPanelCollapsed.value) {
    isSessionPanelCollapsed.value = false
    console.log('右滑手势：展开会话面板')
  }
}

/**
 * 处理移动端关闭会话面板
 */
const handleMobileCloseSessionPanel = () => {
  if (isMobile.value) {
    isSessionPanelCollapsed.value = true
    console.log('移动端关闭会话面板')
  }
}

// 监听会话数据变化，自动保存
watch([moduleSessions, moduleCurrentSession], () => {
  if (sessionsLoaded.value) {
    autoSaveSessionData()
  }
}, { deep: true })

// 监听当前模块变化，确保会话状态同步
watch(currentModule, async (newModule, oldModule) => {
  if (sessionsLoaded.value && newModule !== oldModule) {
    console.log(`🔄 模块切换监听: ${oldModule} -> ${newModule}`)
    await nextTick()
    await validateAndFixCurrentSession()
  }
})

// 监听当前会话变化，确保消息列表同步
watch(currentSession, (newSession, oldSession) => {
  if (sessionsLoaded.value && newSession !== oldSession) {
    console.log('🔄 当前会话变化监听:', {
      oldTitle: oldSession?.title,
      newTitle: newSession?.title,
      newMessagesCount: newSession?.messages?.length || 0
    })
  }
}, { deep: true })

// 组件挂载时初始化
onMounted(async () => {
  console.log('应用初始化开始...')

  // 检查用户登录状态，但如果检查失败则使用预设状态
  console.log('🔍 检查用户登录状态...')
  let isLoggedIn
  try {
    isLoggedIn = await checkUserLoginStatus()
    console.log('✅ 用户登录状态检查完成:', isLoggedIn)
  } catch (error) {
    console.warn('⚠️ 用户登录状态检查失败，使用预设状态:', isUserLoggedIn.value)
    isLoggedIn = isUserLoggedIn.value
  }

  if (isLoggedIn) {
    console.log('用户已登录，继续初始化应用...')

    // 用户已登录时，也要建立Redis连接
    console.log('🔗 检测到用户已登录，建立Redis连接...')
    await initializeRedisConnectionAfterLogin()

    // 恢复正常的会话数据加载
    console.log('📂 开始加载会话数据...')
    await loadSessionData()
    console.log('✅ 会话数据加载完成')

    console.log('应用初始化完成，默认进入知识中心模块')
  } else {
    console.log('用户未登录，等待用户登录...')
    // 不加载会话数据，等待用户登录
  }

  // 将调试函数暴露到全局，方便在控制台调用
  if (typeof window !== 'undefined') {
    // 调试函数
    window.debugSessionState = debugCurrentSessionState
    window.debugSessionCreation = debugSessionCreation
    window.forceRefreshSession = forceRefreshCurrentSession

    // 核心函数
    window.handleSendMessage = handleSendMessage
    window.handleNewSession = handleNewSession

    // 响应式数据
    window.moduleSessions = moduleSessions
    window.moduleCurrentSession = moduleCurrentSession
    window.currentModule = currentModule
    window.sessionsLoaded = sessionsLoaded
    window.isUserLoggedIn = isUserLoggedIn

    // 计算属性
    window.currentModuleSessions = currentModuleSessions
    window.sortedCurrentModuleSessions = sortedCurrentModuleSessions
    window.currentSession = currentSession
    window.currentMessages = currentMessages
    window.needSessionPanel = needSessionPanel
    window.isSessionPanelCollapsed = isSessionPanelCollapsed

    // 工具函数
    window.clearAllSessions = () => {
      console.log('🧹 手动清空所有会话数据')
      moduleSessions.value = {
        knowledge: [],
        business: [],
        function: []
      }
      moduleCurrentSession.value = {
        knowledge: null,
        business: null,
        function: null
      }
      console.log('✅ 所有会话已清空')
    }

    // 强制重新加载会话数据
    window.reloadSessions = async () => {
      console.log('🔄 强制重新加载会话数据')
      sessionsLoaded.value = false
      await loadSessionData()
      console.log('✅ 会话数据重新加载完成')
    }

    window.checkStorageStatus = () => {
      console.log('=== 存储状态检查 ===')
      console.log('存储服务类型:', storageStatus.value.type)
      console.log('Redis连接状态:', storageStatus.value.connected)
      console.log('使用Redis存储:', useRedisStorage.value)
      console.log('存储服务实例:', storageService.value ? '已初始化' : '未初始化')
      if (storageService.value && storageService.value.isSupported !== undefined) {
        console.log('Redis支持状态:', storageService.value.isSupported)
      }
      console.log('会话加载状态:', sessionsLoaded.value)
      return {
        type: storageStatus.value.type,
        connected: storageStatus.value.connected,
        useRedis: useRedisStorage.value,
        serviceInitialized: !!storageService.value,
        redisSupported: storageService.value?.isSupported,
        sessionsLoaded: sessionsLoaded.value
      }
    }
    window.testRedisSave = async () => {
      console.log('=== 测试Redis保存 ===')
      if (storageService.value) {
        try {
          const result = await storageService.value.saveAllSessions(moduleSessions.value, moduleCurrentSession.value)
          console.log('保存结果:', result)
          return result
        } catch (error) {
          console.error('保存失败:', error)
          return false
        }
      } else {
        console.log('存储服务未初始化')
        return false
      }
    }

    // 暴露会话管理函数
    window.handleModuleChange = handleModuleChange
    window.handleSendMessage = handleSendMessage
    window.handleNewSession = handleNewSession

    // 暴露响应式数据
    window.currentModule = currentModule
    window.moduleSessions = moduleSessions
    window.moduleCurrentSession = moduleCurrentSession
    window.sessionsLoaded = sessionsLoaded

    // 暴露Redis连接测试函数
    window.testRedisConnectionAfterLogin = async () => {
      console.log('🧪 手动测试用户登录后的Redis连接...')
      try {
        await initializeRedisConnectionAfterLogin()
        console.log('✅ Redis连接测试完成')
      } catch (error) {
        console.error('❌ Redis连接测试失败:', error)
      }
    }

    // 暴露主题和通知测试函数
    window.testThemeAndNotification = () => {
      console.log('🎨 测试主题切换和通知框颜色...')

      // 获取当前主题
      const currentTheme = document.documentElement.getAttribute('data-theme')
      console.log('当前主题:', currentTheme)

      // 测试不同类型的通知
      const notifications = [
        { message: '成功通知测试 - Moon模式下应显示红色字体', type: 'success' },
        { message: '信息通知测试 - Moon模式下应显示红色字体', type: 'info' },
        { message: '警告通知测试 - Moon模式下应显示红色字体', type: 'warning' },
        { message: '错误通知测试 - Moon模式下应显示红色字体', type: 'error' }
      ]

      notifications.forEach((notif, index) => {
        setTimeout(() => {
          showNotification(notif.message, notif.type, 3000)
        }, index * 1000)
      })
    }

    console.log('🔧 调试函数已暴露到全局:')
    console.log('  - window.debugSessionState() - 调试会话状态')
    console.log('  - window.forceRefreshSession() - 强制刷新会话')
    console.log('  - window.handleModuleChange(module) - 切换模块')
    console.log('  - window.handleSendMessage(content, options) - 发送消息')
    console.log('  - window.handleNewSession(isAutoCreated) - 创建新会话')
    console.log('  - window.testRedisConnectionAfterLogin() - 测试登录后Redis连接')
    console.log('  - window.testThemeAndNotification() - 测试主题切换和通知框颜色')
    console.log('响应式数据:')
    console.log('  - window.currentModule - 当前模块')
    console.log('  - window.moduleSessions - 模块会话列表')
    console.log('  - window.moduleCurrentSession - 当前会话')
    console.log('  - window.sessionsLoaded - 会话加载状态')

    console.log('🔧 调试工具已加载到全局 window 对象')
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (autoSaveTimer) {
    clearTimeout(autoSaveTimer)
  }
  if (notificationTimer) {
    clearTimeout(notificationTimer)
  }
})
</script>

<style scoped>
/* 主布局样式 */
.main-layout {
  display: flex;
  height: 100vh;
  width: 100vw;
  background-color: var(--bg-quaternary);
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

/* 左侧导航栏 */
.sidebar {
  width: var(--sidebar-width-desktop);
  min-width: var(--sidebar-width-desktop);
  height: 100vh;
  background: var(--sidebar-bg);
  border-right: 1px solid var(--border-light);
  z-index: 100;
  flex-shrink: 0;
  margin: 0;
  padding: 0;
  border-left: none;
  transition: width 0.3s ease, min-width 0.3s ease;
}

/* 右侧内容区域 */
.content-area {
  flex: 1;
  display: flex;
  height: 100vh;
  min-width: 0; /* 防止flex子元素溢出 */
  background-color: var(--bg-primary);
  overflow: hidden;
  width: calc(100vw - 60px); /* 明确设置宽度 */
}

/* 会话面板 */
.session-panel {
  width: var(--session-panel-width-desktop);
  min-width: var(--session-panel-width-desktop);
  max-width: var(--session-panel-width-desktop);
  height: 100vh;
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-light);
  z-index: 50;
  flex-shrink: 0;
  position: relative;
  transition: width 0.3s ease, min-width 0.3s ease, max-width 0.3s ease;
}

/* 折叠状态的会话面板 */
.session-panel.collapsed {
  width: var(--session-panel-collapsed-width);
  min-width: var(--session-panel-collapsed-width);
  max-width: var(--session-panel-collapsed-width);
  border-right: none;
}

/* 会话面板折叠切换按钮 */
.session-panel-toggle {
  position: absolute;
  top: 20px;
  right: -12px;
  width: 24px;
  height: 24px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 60;
  transition: all 0.2s ease;
  color: var(--text-secondary);
}

/* 折叠状态下的切换按钮 */
.session-panel.collapsed .session-panel-toggle {
  right: -12px;
  background: var(--accent-primary);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.session-panel-toggle:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  transform: scale(1.1);
}

.session-panel.collapsed .session-panel-toggle:hover {
  background: var(--accent-secondary);
  transform: scale(1.1);
}

.session-panel-toggle svg {
  width: 12px;
  height: 12px;
}

/* 对话区域 */
.chat-area {
  flex: 1;
  height: 100vh;
  background-color: var(--bg-primary);
  min-width: 300px; /* 确保对话区域有最小宽度 */
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 帮助与反馈模块全宽显示 */
.chat-area.full-width {
  width: calc(100vw - 60px); /* 减去左侧导航栏宽度 */
  min-width: calc(100vw - 60px);
}

/* 会话面板折叠时的聊天区域 */
.chat-area.session-panel-collapsed {
  width: calc(100vw - 110px - 24px); /* 减去左侧导航栏和折叠后的会话面板宽度 */
  transition: width 0.3s ease;
}

/* 顶部工具栏 */
.toolbar {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 200;
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: nowrap;
  justify-content: flex-end;
}

/* 移动端隐藏悬浮工具栏 */
.mobile-toolbar {
  display: none;
}

/* 通知组件样式 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideInRight 0.3s ease-out;
}

.notification-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: var(--bg-primary);
  border-radius: 8px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.notification-message {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  color: var(--text-primary);
  font-weight: 500;
}

/* 深色模式下通知框基础样式增强 */
[data-theme="moon"] .notification-content {
  background-color: rgba(30, 30, 30, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px #f00c0cec;
}

[data-theme="moon"] .notification-message {
  color: #ff4d4f;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.notification-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: var(--text-secondary);
  margin-left: 12px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-close:hover {
  color: var(--text-primary);
}

/* 不同类型的通知样式 */
.notification-success {
  border-left: 4px solid #52c41a;
}

.notification-success .notification-content {
  background-color: var(--notification-success-bg, #f6ffed);
  border: 1px solid var(--notification-success-border, #b7eb8f);
  color: var(--notification-success-text, #135200);
}

.notification-info {
  border-left: 4px solid #1890ff;
}[data-theme="moon"] .notification-message {
  color: #ff4d4f;  /* 红色 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.notification-info .notification-content {
  background-color: var(--notification-info-bg, #e6f7ff);
  border: 1px solid var(--notification-info-border, #91d5ff);
  color: var(--notification-info-text, #003a8c);
}

.notification-warning {
  border-left: 4px solid #faad14;
}

.notification-warning .notification-content {
  background-color: var(--notification-warning-bg, #fffbe6);
  border: 1px solid var(--notification-warning-border, #ffe58f);
  color: var(--notification-warning-text, #613400);
}

.notification-error {
  border-left: 4px solid #ff4d4f;
}

.notification-error .notification-content {
  background-color: var(--notification-error-bg, #fff2f0);
  border: 1px solid var(--notification-error-border, #ffccc7);
  color: var(--notification-error-text, #a8071a);
}

/* 深色模式下的通知样式 */
[data-theme="moon"] .notification-success .notification-content {
  background-color: rgba(82, 196, 26, 0.15);
  border: 1px solid rgba(82, 196, 26, 0.3);
  color: #95de64;
}

[data-theme="moon"] .notification-info .notification-content {
  background-color: rgba(24, 144, 255, 0.15);
  border: 1px solid rgba(24, 144, 255, 0.3);
  color: #69c0ff;
}

[data-theme="moon"] .notification-warning .notification-content {
  background-color: rgba(250, 173, 20, 0.15);
  border: 1px solid rgba(250, 173, 20, 0.3);
  color: #ffd666;
}

[data-theme="moon"] .notification-error .notification-content {
  background-color: rgba(255, 77, 79, 0.15);
  border: 1px solid rgba(255, 77, 79, 0.3);
  color: #ff7875;
}

/* 深色模式下通知关闭按钮样式 */
[data-theme="moon"] .notification-close {
  color: var(--text-secondary);
}

[data-theme="moon"] .notification-close:hover {
  color: var(--text-primary);
}

/* 动画效果 */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */

/* 平板端适配 (1024px以下) */
@media (max-width: 1024px) {
  .sidebar {
    width: var(--sidebar-width-tablet);
    min-width: var(--sidebar-width-tablet);
  }

  .session-panel {
    width: var(--session-panel-width-tablet);
    min-width: var(--session-panel-width-tablet);
    max-width: var(--session-panel-width-tablet);
  }

  .session-panel.collapsed {
    width: var(--session-panel-collapsed-width);
    min-width: var(--session-panel-collapsed-width);
    max-width: var(--session-panel-collapsed-width);
  }

  .chat-area.full-width {
    width: calc(100vw - var(--sidebar-width-tablet));
    min-width: calc(100vw - var(--sidebar-width-tablet));
  }

  .chat-area.session-panel-collapsed {
    width: calc(100vw - var(--sidebar-width-tablet) - var(--session-panel-collapsed-width));
  }

  .toolbar {
    top: var(--spacing-md);
    right: var(--spacing-md);
    gap: var(--spacing-sm);
  }
}

/* 移动端适配 (768px以下) - 抽屉式会话面板 */
@media (max-width: 768px) {
  .main-layout {
    position: relative;
    overflow: hidden;
  }

  .sidebar {
    width: var(--sidebar-width-mobile);
    min-width: var(--sidebar-width-mobile);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 200;
    box-shadow: var(--shadow-lg);
    height: 100vh;
  }

  .content-area {
    margin-left: var(--sidebar-width-mobile);
    width: calc(100vw - var(--sidebar-width-mobile));
    height: 100vh;
    position: relative;
  }

  /* 移动端会话面板 - 抽屉式 */
  .session-panel {
    width: 85vw; /* 占屏幕85%宽度 */
    max-width: 320px;
    min-width: 280px;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 300;
    box-shadow: var(--shadow-xl);
    transform: translateX(-100%); /* 默认隐藏在左侧 */
    transition: transform 0.3s ease;
    height: 100vh;
    background: var(--bg-primary);
  }

  /* 展开状态的会话面板 */
  .session-panel:not(.collapsed) {
    transform: translateX(0); /* 滑入显示 */
  }

  /* 折叠状态保持隐藏 */
  .session-panel.collapsed {
    transform: translateX(-100%);
  }

  /* 聊天区域占满剩余空间 */
  .chat-area {
    margin-left: 0;
    width: 100%;
    height: 100vh;
    position: relative;
  }

  .chat-area.full-width {
    margin-left: 0;
    width: 100%;
  }

  .chat-area.session-panel-collapsed {
    margin-left: 0;
    width: 100%;
  }

  /* 会话面板遮罩层 */
  .session-panel:not(.collapsed)::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
    opacity: 1;
    transition: opacity 0.3s ease;
  }

  /* 移动端会话管理按钮 */
  .mobile-session-button {
    position: fixed;
    top: 20px;
    left: calc(var(--sidebar-width-mobile) + 16px);
    z-index: 250;
    background: var(--accent-primary);
    color: white;
    border: none;
    border-radius: 24px;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    touch-action: manipulation;
  }

  .mobile-session-button:hover {
    background: var(--accent-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
  }

  .mobile-session-button:active {
    transform: translateY(0);
  }

  .mobile-session-button svg {
    width: 18px;
    height: 18px;
  }

  .button-text {
    font-size: var(--font-size-xs);
  }

  /* 隐藏原有的会话面板切换按钮 */
  .session-panel-toggle {
    display: none;
  }

  /* 隐藏桌面端工具栏 */
  .toolbar {
    display: none;
  }

  /* 移动端悬浮工具栏 */
  .mobile-toolbar {
    position: fixed;
    top: 20px;
    right: 16px;
    z-index: 250;
    display: flex;
    gap: 8px;
    align-items: center;
  }

  /* 移动端工具栏项目样式 */
  .mobile-toolbar .mobile-toolbar-item {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 8px 12px;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
  }

  .mobile-toolbar .mobile-toolbar-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
  }

  /* 移动端隐藏登录按钮 - 使用更兼容的选择器 */
  .mobile-toolbar .login-trigger,
  .mobile-toolbar .user-info {
    display: none !important;
  }

  .session-panel-toggle {
    right: -10px;
    width: 20px;
    height: 20px;
  }

  .session-panel-toggle svg {
    width: 10px;
    height: 10px;
  }
}

/* 小屏手机适配 (480px以下) */
@media (max-width: 480px) {
  .mobile-session-button {
    top: 12px;
    left: calc(var(--sidebar-width-mobile) + 8px);
    padding: 6px 12px;
    border-radius: 20px;
  }

  .mobile-session-button svg {
    width: 16px;
    height: 16px;
  }

  .button-text {
    font-size: 10px;
  }

  .session-panel {
    width: calc(100vw - 16px);
    max-width: calc(100vw - 16px);
    min-width: calc(100vw - 16px);
  }

  /* 隐藏桌面端工具栏 */
  .toolbar {
    display: none;
  }

  /* 移动端悬浮工具栏 - 小屏适配 */
  .mobile-toolbar {
    top: 12px;
    right: 8px;
    gap: 6px;
  }

  .mobile-toolbar .mobile-toolbar-item {
    padding: 6px 10px;
    border-radius: 16px;
  }

  /* 小屏手机模式下隐藏登录按钮 - 使用更兼容的选择器 */
  .mobile-toolbar .login-trigger,
  .mobile-toolbar .user-info {
    display: none !important;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .mobile-toolbar .mobile-toolbar-item {
    background: rgba(31, 41, 55, 0.95) !important;
    border-color: rgba(75, 85, 99, 0.3) !important;
  }
}

/* 超大屏幕适配 (1440px以上) */
@media (min-width: 1440px) {
  .sidebar {
    width: 120px;
    min-width: 120px;
  }

  .session-panel {
    width: 280px;
    min-width: 280px;
    max-width: 280px;
  }

  .toolbar {
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    gap: var(--spacing-md);
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-light);
  border-top: 3px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  font-size: 14px;
  color: var(--text-secondary);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
