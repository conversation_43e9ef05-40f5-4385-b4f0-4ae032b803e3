<template>
  <!-- 职能域页面 - 包含功能选择器 -->
  <SimpleWatermark module="function">
    <div class="function-domain">
    <!-- 聊天头部区域 -->
    <div class="chat-header">
      <div class="ai-avatar">
        <div class="avatar-icon">⚙️</div>
      </div>
      <div class="ai-info">
        <h2 class="ai-name">小邦同学智能搜索</h2>
        <p class="ai-description">欢迎使用***知识库，我可以帮您快速查找资料并提供专业解答，有什么问题尽管问我吧～</p>
      </div>
    </div>

    <!-- 消息列表区域 -->
    <div class="message-container" ref="messageContainer">
      <div class="message-list">
        <!-- 欢迎消息（仅在没有消息时显示） -->
        <div v-if="messages.length === 0" class="welcome-message">
          <div class="welcome-content">
            <div class="welcome-icon"><img src="../../../public/favicon.ico" alt="AI头像" style="width: 20%; height: 20%; object-fit: cover;"></div>
            <h3>职能域功能服务</h3>
            <p>请选择需要的职能功能，我将为您提供专业服务</p>
          </div>
        </div>
        
        <!-- 消息列表 -->
        <div 
          v-for="message in messages" 
          :key="message.id"
          class="message-item"
          :class="{ 'user-message': message.type === 'user', 'ai-message': message.type === 'ai' }"
        >
          <!-- 用户消息 -->
          <div v-if="message.type === 'user'" class="message-content user-content">
            <div class="message-bubble user-bubble">
              {{ message.content }}
              <!-- 显示选择的功能 -->
              <div v-if="message.selectedFunction" class="message-meta">
                <span class="meta-label">功能:</span>
                <span class="meta-value">{{ message.selectedFunction }}</span>
              </div>
            </div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
          
          <!-- AI消息 -->
          <AIMessage
            v-else
            :message="message"
            :streaming-content="message.streamingContent"
            @retry="handleRetryMessage"
          />
        </div>
        
        <!-- 加载状态现在由消息本身的loading属性控制 -->
      </div>
    </div>

    <!-- 功能选择器 -->
    <div class="function-selector" :class="{ 'highlight': !selectedFunction }">
      <div class="selector-container">
        <div class="selector-header">
          <span class="selector-title">选择职能功能:</span>
          <span v-if="!selectedFunction" class="selector-hint">请先选择一个职能功能</span>
        </div>
        <div class="function-categories">
          <!-- 分类显示功能选项 -->
          <div
            v-for="category in functionCategories"
            :key="category.id"
            class="function-category"
          >
            <div class="category-title">{{ category.name }}</div>
            <div class="function-options">
              <button
                v-for="func in category.functions"
                :key="func.id"
                class="function-option"
                :class="{ 'active': selectedFunction === func.id }"
                @click="selectFunction(func.id)"
              >
                <div class="option-icon">{{ func.icon }}</div>
                <div class="option-text">{{ func.name }}</div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <div class="input-container">
        <div class="input-wrapper">
          <textarea
            v-model="inputText"
            ref="textareaRef"
            class="message-input"
            :placeholder="inputPlaceholder"
            rows="1"
            @keydown="handleKeydown"
            @input="adjustTextareaHeight"
          ></textarea>
          
          <div class="input-actions">
            <button
              class="action-button clear-button"
              @click="clearChat"
              title="清空对话"
              :disabled="messages.length === 0"
            >
              <ClearIcon />
            </button>
          </div>

          <button
            class="send-button"
            :disabled="!canSendMessage"
            @click="handleSendMessage"
            :title="!selectedFunction ? '请先选择职能功能' : !inputText.trim() ? '请输入消息内容' : '发送消息'"
          >
            <SendIcon />
          </button>
        </div>
      </div>
    </div>
    </div>
  </SimpleWatermark>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue'
import AIMessage from '../common/AIMessage.vue'
import SimpleWatermark from '../common/SimpleWatermark.vue'

// 发送图标组件
const SendIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
    </svg>
  `
}

// 清空图标组件
const ClearIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
    </svg>
  `
}

// 组件属性
const props = defineProps({
  currentSession: {
    type: Object,
    default: null
  },
  messages: {
    type: Array,
    default: () => []
  }
})

// 组件事件
const emit = defineEmits(['send-message', 'clear-messages'])

// 响应式数据
const inputText = ref('')
const isLoading = ref(false)
const messageContainer = ref(null)
const textareaRef = ref(null)
const selectedFunction = ref('')



// 职能功能分类配置
const functionCategories = ref([
  // {
  //   id: 'analysis',
  //   name: '数据分析',
  //   functions: [
  //     { id: 'data-analysis', name: '数据分析报告', icon: '📊' },
  //     { id: 'trend-analysis', name: '趋势分析', icon: '📈' },
  //     { id: 'performance-analysis', name: '绩效分析', icon: '🎯' }
  //   ]
  // },
  // {
  //   id: 'document',
  //   name: '文档处理',
  //   functions: [
  //     { id: 'doc-summary', name: '文档摘要', icon: '📄' },
  //     { id: 'doc-translation', name: '文档翻译', icon: '🌐' },
  //     { id: 'doc-format', name: '格式转换', icon: '🔄' }
  //   ]
  // },
  // {
  //   id: 'planning',
  //   name: '规划制定',
  //   functions: [
  //     { id: 'project-plan', name: '项目规划', icon: '📋' },
  //     { id: 'schedule-plan', name: '时间安排', icon: '📅' },
  //     { id: 'resource-plan', name: '资源配置', icon: '🎲' }
  //   ]
  // },
  {
    id: 'communication',
    name: '业务提效',
    functions: [
      { id: 'meeting-summary', name: 'PPT生成', icon: '📝' },
      { id: 'code-draft', name: '代码助手', icon: '🎨' },
      { id: 'ht-draft', name: '合同审批', icon: '📋' },
      { id: 'bodor-help', name: '小邦助手', icon: '✨' },
      { id: 'ocr-draft', name: 'OCR识别', icon: '🔍' }
    ]
  }
])

// 计算属性
const inputPlaceholder = computed(() => {
  if (!selectedFunction.value) {
    return '请先选择职能功能，然后输入需求'
  }
  const funcName = getFunctionName(selectedFunction.value)
  return `使用${funcName}功能，发送[Enter]/换行[Ctrl+Enter]`
})

const canSendMessage = computed(() => {
  return inputText.value.trim() && selectedFunction.value && !isLoading.value
})

/**
 * 获取功能名称
 * @param {string} functionId - 功能ID
 * @returns {string} 功能名称
 */
const getFunctionName = (functionId) => {
  for (const category of functionCategories.value) {
    const func = category.functions.find(f => f.id === functionId)
    if (func) return func.name
  }
  return ''
}

/**
 * 选择功能
 * @param {string} functionId - 功能ID
 */
const selectFunction = (functionId) => {
  selectedFunction.value = functionId
  const funcName = getFunctionName(functionId)
  console.log('选择职能功能:', funcName)
}

/**
 * 格式化时间显示
 */
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  
  const date = new Date(timestamp)
  const now = new Date()
  const isToday = date.toDateString() === now.toDateString()
  
  if (isToday) {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

/**
 * 自动调整输入框高度
 */
const adjustTextareaHeight = () => {
  nextTick(() => {
    const textarea = textareaRef.value
    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
    }
  })
}

/**
 * 滚动到消息列表底部
 */
const scrollToBottom = () => {
  nextTick(() => {
    if (messageContainer.value) {
      messageContainer.value.scrollTop = messageContainer.value.scrollHeight
    }
  })
}

/**
 * 处理键盘事件
 */
const handleKeydown = (event) => {
  if (event.key === 'Enter') {
    if (event.ctrlKey) {
      return
    } else {
      event.preventDefault()
      handleSendMessage()
    }
  }
}

/**
 * 发送消息
 */
const handleSendMessage = () => {
  const content = inputText.value.trim()
  if (!canSendMessage.value) return
  
  const funcName = getFunctionName(selectedFunction.value)
  
  console.log('职能域发送消息:', content, '功能:', funcName)

  // 发送消息事件，包含选择的功能信息
  emit('send-message', content, {
    selectedFunction: funcName,
    functionId: selectedFunction.value
  })

  inputText.value = ''
  adjustTextareaHeight()
  scrollToBottom()
}

/**
 * 处理重试消息
 */
const handleRetryMessage = () => {
  // 重试逻辑可以在这里实现
  console.log('重试AI消息')
}

/**
 * 清空对话
 */
const clearChat = () => {
  if (props.messages.length === 0) return

  if (confirm('确定要清空所有对话记录吗？此操作不可撤销。')) {
    console.log('清空职能域对话')
    emit('clear-messages')
  }
}

// 监听消息变化
watch(() => props.messages, (newMessages, oldMessages) => {
  console.log('📨 FunctionDomain 消息变化:', {
    newCount: newMessages?.length || 0,
    oldCount: oldMessages?.length || 0,
    sessionTitle: props.currentSession?.title
  })
  scrollToBottom()
}, { deep: true })

// 监听当前会话变化
watch(() => props.currentSession, (newSession, oldSession) => {
  console.log('🔄 FunctionDomain 会话变化:', {
    oldTitle: oldSession?.title,
    newTitle: newSession?.title,
    messagesCount: newSession?.messages?.length || 0
  })

  // 恢复职能功能选择状态
  restoreFunctionSelection(newSession)
}, { deep: true })

/**
 * 根据会话消息历史恢复职能功能选择状态
 * @param {Object} session - 当前会话对象
 */
const restoreFunctionSelection = (session) => {
  if (!session || !session.messages || session.messages.length === 0) {
    // 没有会话或消息，清空选择状态
    selectedFunction.value = ''
    console.log('🔄 FunctionDomain: 清空职能功能选择状态（无消息历史）')
    return
  }

  // 从最后一条用户消息中查找职能功能选择信息
  const userMessages = session.messages.filter(msg => msg.type === 'user')
  if (userMessages.length === 0) {
    selectedFunction.value = ''
    console.log('🔄 FunctionDomain: 清空职能功能选择状态（无用户消息）')
    return
  }

  // 获取最后一条用户消息的职能功能信息
  const lastUserMessage = userMessages[userMessages.length - 1]
  if (lastUserMessage.functionId) {
    selectedFunction.value = lastUserMessage.functionId
    const funcName = getFunctionName(lastUserMessage.functionId)
    console.log('🔄 FunctionDomain: 恢复职能功能选择状态:', funcName)
  } else {
    selectedFunction.value = ''
    console.log('🔄 FunctionDomain: 清空职能功能选择状态（消息无职能功能信息）')
  }
}
</script>

<style scoped>
/* 职能域容器 */
.function-domain {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  overflow: hidden;
}

/* 聊天头部样式（与其他页面相同） */
.chat-header {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-secondary);
  background-color: var(--bg-secondary);
}

.ai-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.avatar-icon {
  font-size: 24px;
}

.ai-info h2 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.ai-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

/* 消息容器 */
.message-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 24px;
}

.message-list {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px 0;
}

/* 欢迎消息 */
.welcome-message {
  text-align: center;
  padding: 60px 20px;
}

.welcome-content {
  max-width: 400px;
  margin: 0 auto;
}

.welcome-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.welcome-content h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
  margin-bottom: 8px;
}

.welcome-content p {
  font-size: 14px;
  color: var(--text-secondary, #6b7280);
  line-height: 1.6;
}

/* 消息样式 */
.message-item {
  margin-bottom: 16px;
}

.message-content {
  display: flex;
  align-items: flex-end;
}

.user-content {
  justify-content: flex-end;
  flex-direction: column;
  align-items: flex-end;
}

.user-bubble {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-left: 60px;
}

.ai-content {
  justify-content: flex-start;
}

.ai-avatar-small {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-right: 12px;
  flex-shrink: 0;
}

.ai-message-wrapper {
  flex: 1;
  max-width: calc(100% - 44px);
}

.ai-bubble {
  background-color: var(--bg-secondary, #f8fafc);
  color: var(--text-primary, #1f2937);
  border: 1px solid var(--border-primary, #e2e8f0);
  margin-right: 60px;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
  max-width: 100%;
}

/* 消息元数据 */
.message-meta {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-label {
  opacity: 0.8;
}

.meta-value {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
}

.message-time {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
  text-align: right;
}

.ai-content .message-time {
  text-align: left;
}

/* 加载状态 */
.loading {
  background-color: #f8fafc !important;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #9ca3af;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

/* 功能选择器 */
.function-selector {
  border-top: 1px solid var(--border-secondary);
  border-bottom: 1px solid var(--border-secondary);
  background-color: var(--bg-secondary);
  padding: 12px 24px; /* 减少内边距 */
  max-height: 150px; /* 将高度从300px减少到150px */
  overflow-y: auto;
  transition: all 0.3s ease;
  /* 确保选择器紧贴输入区域 */
  margin-bottom: 0;
}

.function-selector.highlight {
  background-color: #fff7e6;
  border-top: 2px solid #ffa940;
  animation: pulse-highlight 2s infinite;
}

@keyframes pulse-highlight {
  0%, 100% { background-color: #fff7e6; }
  50% { background-color: #fff2d9; }
}

.selector-container {
  max-width: 800px;
  margin: 0 auto;
}

.selector-header {
  margin-bottom: 12px;
}

.selector-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.selector-hint {
  margin-left: 8px;
  color: #ff7875;
  font-size: 12px;
  font-weight: 500;
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

.function-categories {
  display: flex;
  flex-direction: column;
  gap: 8px; /* 减少分类间距从16px到8px */
}

.function-category {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 6px; /* 减少圆角 */
  padding: 8px; /* 减少内边距从12px到8px */
}

.category-title {
  font-size: 12px; /* 减少字体大小 */
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 6px; /* 减少下边距从8px到6px */
  padding-bottom: 3px; /* 减少下内边距从4px到3px */
  border-bottom: 1px solid var(--border-secondary);
}

.function-options {
  display: flex;
  flex-wrap: wrap;
  gap: 4px; /* 减少选项间距从6px到4px */
}

.function-option {
  display: flex;
  align-items: center;
  gap: 4px; /* 减少图标和文字间距从6px到4px */
  padding: 4px 8px; /* 减少内边距从6px 10px到4px 8px */
  border: 1px solid var(--border-primary);
  border-radius: 4px; /* 减少圆角从6px到4px */
  background-color: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 11px; /* 减少字体大小从12px到11px */
}

.function-option:hover {
  border-color: var(--accent-primary);
  background-color: var(--accent-secondary);
}

.function-option.active {
  border-color: var(--accent-primary);
  background-color: var(--accent-primary);
  color: var(--text-white);
}

.option-icon {
  font-size: 12px;
}

.option-text {
  white-space: nowrap;
}

/* 输入区域 */
.input-area {
  /* 移除顶部边框，因为选择器已经有底部边框了 */
  background-color: var(--bg-primary);
  padding: 16px 24px;
}

.input-container {
  max-width: 800px;
  margin: 0 auto;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  padding: 12px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  transition: border-color 0.2s ease;
}

.input-wrapper:focus-within {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.message-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  color: var(--text-primary);
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  min-height: 20px;
  max-height: 120px;
}

.message-input::placeholder {
  color: var(--text-tertiary);
}

.send-button {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: var(--accent-primary);
  color: var(--text-white);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.send-button svg {
  width: 16px;
  height: 16px;
}

/* 输入操作按钮 */
.input-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-button {
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  background: var(--bg-primary);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.action-button:hover:not(:disabled) {
  border-color: var(--accent-primary);
  color: var(--accent-primary);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-button svg {
  width: 16px;
  height: 16px;
}

.clear-button:hover:not(:disabled) {
  border-color: #ef4444;
  color: #ef4444;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
}

/* 加载和错误状态消息 */
.loading-message {
  background-color: #f8fafc !important;
  position: relative;
}

.error-message {
  background-color: #fef2f2 !important;
  border-left: 3px solid #ef4444;
}

/* 加载动画 */
.loading-dots {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 0;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #9ca3af;
  animation: loading-bounce 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
</style>
