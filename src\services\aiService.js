/**
 * AI对话服务
 * 支持OneAPI格式的API调用，包含模拟模式
 */

import { getAIConfig, validateAIConfig } from '../config/ai.js'
import { getUserService } from './userService.js'

/**
 * AI对话服务类
 */
export class AIService {
  constructor(module) {
    this.module = module
    this.config = getAIConfig(module)
    this.userService = getUserService()

    // 验证配置
    if (!validateAIConfig(this.config)) {
      console.error(`模块 ${module} 的AI配置不完整，请检查配置文件`)
    }
  }

  /**
   * 发送消息到AI并获取回复（非流式）
   * @param {Array} messages - 消息历史数组
   * @param {Object} options - 额外选项
   * @returns {Promise<string>} AI回复内容
   */
  async sendMessage(messages, options = {}) {
    try {
      // 检查是否启用模拟模式或AI服务不可用时使用模拟回复
      if (this.config.enableMockMode) {
        try {
          // 先尝试真实的AI服务
          return await this.sendRealMessage(messages, options)
        } catch (error) {
          console.warn(`[${this.module}] AI服务不可用，使用模拟回复:`, error.message)
          return this.generateMockResponse(messages)
        }
      } else {
        return await this.sendRealMessage(messages, options)
      }
    } catch (error) {
      console.error(`[${this.module}] AI服务调用失败:`, error)
      throw error
    }
  }

  /**
   * 发送真实的AI请求
   * @param {Array} messages - 消息历史数组
   * @param {Object} options - 额外选项
   * @returns {Promise<string>} AI回复内容
   */
  async sendRealMessage(messages, options = {}) {
    try {
      // 构建请求消息数组
      const requestMessages = await this.buildMessages(messages)

      // 构建请求体
      const requestBody = {
        model: this.config.model,
        messages: requestMessages,
        max_tokens: options.maxTokens || this.config.maxTokens,
        temperature: options.temperature || this.config.temperature,
        stream: false
      }

      console.log(`[${this.module}] 发送AI请求:`, {
        model: requestBody.model,
        messageCount: requestMessages.length,
        lastMessage: requestMessages[requestMessages.length - 1]?.content?.substring(0, 100)
      })

      // 发送请求
      const response = await this.makeRequest(requestBody)

      // 解析响应
      const aiReply = this.parseResponse(response)

      console.log(`[${this.module}] 收到AI回复:`, aiReply.substring(0, 100))

      return aiReply

    } catch (error) {
      console.error(`[${this.module}] AI请求失败:`, error)
      throw error
    }
  }

  /**
   * 生成模拟回复
   * @param {Array} messages - 消息历史数组
   * @returns {string} 模拟的AI回复
   */
  generateMockResponse(messages) {
    const lastMessage = messages[messages.length - 1]
    const userContent = lastMessage?.content || ''
    
    // 根据模块生成不同的模拟回复
    const mockResponses = {
      knowledge: [
        '<thinking>\n我需要仔细分析这个问题。首先理解用户的具体需求，然后从我的知识库中检索相关信息，最后组织一个清晰、准确的回答。\n</thinking>\n\n感谢您的问题！根据知识库的信息，我为您提供以下解答：\n\n**主要内容：**\n- 这是一个模拟回复，用于演示系统功能\n- 实际使用时会连接到真实的AI服务\n- 请检查网络连接和AI服务配置\n\n**建议：**\n1. 确认AI服务正常运行\n2. 检查网络连接状态\n3. 验证API密钥配置',
        '基于您的询问，我从知识库中找到了相关信息：\n\n**解答：**\n这是一个演示回复，展示了系统的基本功能。在正常情况下，这里会显示来自AI模型的真实回复。\n\n**相关资料：**\n- 系统支持多模块对话\n- 会话数据会自动保存\n- 支持Markdown格式显示'
      ],
      business: [
        '<thinking>\n这是一个业务问题，我需要从多个角度来分析：市场环境、竞争态势、内部资源、风险因素等。让我系统地思考一下最佳的解决方案。\n</thinking>\n\n感谢您的咨询！作为您的业务顾问，我为您提供以下分析：\n\n**业务分析：**\n- 这是一个业务域的模拟回复，用于演示业务咨询功能\n- 实际使用时会提供专业的业务分析和建议\n- 请检查业务数据源和相关配置\n\n**建议：**\n1. 分析当前业务状况和市场环境\n2. 制定短期和长期发展策略\n3. 优化业务流程和运营效率',
        '基于您的业务需求，我从业务知识库中找到了相关信息：\n\n**解答：**\n这是一个业务咨询演示回复，展示了业务域的基本功能。在正常情况下，这里会显示来自AI模型的专业业务建议。\n\n**相关建议：**\n- 系统支持多种业务场景分析\n- 业务数据会自动保存和分析\n- 支持业务报告和图表展示'
      ],
      function: [
        '<thinking>\n用户需要功能方面的帮助，我需要提供清晰的步骤指导。让我想想最简单有效的方法，并考虑可能遇到的问题和解决方案。\n</thinking>\n\n感谢您的咨询！功能助手为您提供以下指导：\n\n**功能说明：**\n- 这是一个职能域的模拟回复，用于演示功能指导服务\n- 实际使用时会提供具体的操作步骤和技术支持\n- 请检查功能模块和相关配置\n\n**建议：**\n1. 确认功能需求和技术要求\n2. 按照操作步骤逐步执行\n3. 验证功能实现效果',
        '基于您的功能需求，我从技术知识库中找到了相关信息：\n\n**解答：**\n这是一个功能指导演示回复，展示了职能域的基本功能。在正常情况下，这里会显示来自AI模型的具体操作指导。\n\n**相关资源：**\n- 系统支持多种功能模块\n- 操作记录会自动保存\n- 支持代码示例和最佳实践展示'
      ]
    }
    
    const moduleResponses = mockResponses[this.module] || mockResponses.knowledge
    const randomResponse = moduleResponses[Math.floor(Math.random() * moduleResponses.length)]
    
    // 添加一些个性化内容
    let personalizedResponse = randomResponse
    if (userContent.length > 0) {
      personalizedResponse = `针对您的问题"${userContent.substring(0, 50)}${userContent.length > 50 ? '...' : ''}"，${randomResponse}`
    }
    
    console.log(`[${this.module}] 生成模拟回复`)
    return personalizedResponse
  }

  /**
   * 发送消息到AI并获取流式回复
   * @param {Array} messages - 消息历史数组
   * @param {Function} onChunk - 流式数据回调函数
   * @param {Function} onReasoning - 推理过程回调函数
   * @param {Object} options - 额外选项
   * @returns {Promise<string>} 完整的AI回复内容
   */
  async sendMessageStream(messages, onChunk, onReasoning, options = {}) {
    try {
      // 检查是否启用模拟模式或AI服务不可用时使用模拟回复
      if (this.config.enableMockMode) {
        try {
          // 先尝试真实的AI服务
          return await this.sendRealMessageStream(messages, onChunk, onReasoning, options)
        } catch (error) {
          console.warn(`[${this.module}] AI流式服务不可用，使用模拟回复:`, error.message)
          return this.generateMockStreamResponse(messages, onChunk, onReasoning)
        }
      } else {
        return await this.sendRealMessageStream(messages, onChunk, onReasoning, options)
      }
    } catch (error) {
      console.error(`[${this.module}] AI流式服务调用失败:`, error)
      throw error
    }
  }

  /**
   * 生成模拟流式回复
   * @param {Array} messages - 消息历史数组
   * @param {Function} onChunk - 流式数据回调函数
   * @param {Function} onReasoning - 推理过程回调函数
   * @returns {Promise<string>} 完整的模拟回复
   */
  async generateMockStreamResponse(messages, onChunk, onReasoning) {
    const mockResponse = this.generateMockResponse(messages)

    // 解析模拟回复中的思考过程和最终答案
    const parsed = this.parseThinkingContent(mockResponse)
    const reasoningContent = parsed.thinking || '我正在分析您的问题，考虑最佳的解决方案...'
    const textContent = parsed.finalAnswer || mockResponse

    console.log(`[${this.module}] 开始模拟流式回复，包含推理过程`)

    // 第一阶段：流式输出推理过程
    if (onReasoning && reasoningContent) {
      console.log(`[${this.module}] 模拟模式：开始流式输出推理过程`)
      const reasoningWords = reasoningContent.split('')
      let fullReasoningContent = ''

      for (let i = 0; i < reasoningWords.length; i += 3) {
        const chunk = reasoningWords.slice(i, i + 3).join('')
        fullReasoningContent += chunk

        // 调用推理过程回调
        onReasoning(chunk, fullReasoningContent)

        // 模拟推理思考延迟
        await new Promise(resolve => setTimeout(resolve, 80))
      }

      console.log(`[${this.module}] 推理过程输出完成`)
      // 短暂停顿，模拟从推理到回答的切换
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    // 第二阶段：流式输出文本内容
    console.log(`[${this.module}] 开始流式输出文本内容`)
    const textWords = textContent.split('')
    let fullTextContent = ''

    for (let i = 0; i < textWords.length; i += 2) {
      const chunk = textWords.slice(i, i + 2).join('')
      fullTextContent += chunk

      // 调用文本内容回调
      if (onChunk) {
        onChunk(chunk, fullTextContent)
      }

      // 模拟文本输出延迟
      await new Promise(resolve => setTimeout(resolve, 50))
    }

    console.log(`[${this.module}] 模拟流式回复完成`)
    return fullTextContent
  }

  /**
   * 发送真实的AI流式请求
   * @param {Array} messages - 消息历史数组
   * @param {Function} onChunk - 流式数据回调函数
   * @param {Function} onReasoning - 推理过程回调函数
   * @param {Object} options - 额外选项
   * @returns {Promise<string>} 完整的AI回复内容
   */
  async sendRealMessageStream(messages, onChunk, onReasoning, options = {}) {
    try {
      // 构建请求消息数组
      const requestMessages = await this.buildMessages(messages)

      // 构建请求体
      const requestBody = {
        model: this.config.model,
        messages: requestMessages,
        max_tokens: options.maxTokens || this.config.maxTokens,
        temperature: options.temperature || this.config.temperature,
        detail: true,
        stream: true
      }

      console.log(`[${this.module}] 发送AI流式请求:`, {
        model: requestBody.model,
        messageCount: requestMessages.length,
        lastMessage: requestMessages[requestMessages.length - 1]?.content?.substring(0, 100)
      })

      // 发送流式请求，直接传递推理过程回调
      // 新的AI服务会在响应中包含reasoning和text，不需要预先生成模拟推理过程
      console.log(`[${this.module}] 发送流式请求，支持推理过程和文本内容`)
      const fullContent = await this.makeStreamRequest(requestBody, onChunk, onReasoning)

      console.log(`[${this.module}] 流式回复完成:`, fullContent.substring(0, 100))

      return fullContent

    } catch (error) {
      console.error(`[${this.module}] AI流式请求失败:`, error)
      throw error
    }
  }

  /**
   * 构建消息数组
   * @param {Array} messages - 原始消息数组
   * @returns {Promise<Array>} 格式化的消息数组
   */
  async buildMessages(messages) {
    const requestMessages = []

    // 添加系统提示词
    if (this.config.systemPrompt) {
      requestMessages.push({
        role: 'system',
        content: this.config.systemPrompt
      })
    }

    // 根据配置限制历史对话轮数
    const filteredMessages = this.filterMessagesByHistoryTurns(messages)

    console.log(`[${this.module}] 历史对话配置:`, {
      totalMessages: messages.length,
      historyTurns: this.config.historyTurns,
      filteredMessages: filteredMessages.length,
      actualTurns: Math.floor(filteredMessages.length / 2)
    })

    // 获取当前用户信息
    let userInfo = null
    try {
      userInfo = await this.userService.getCurrentUser()
      console.log(`[${this.module}] 获取到用户信息:`, userInfo)
    } catch (error) {
      console.warn(`[${this.module}] 获取用户信息失败:`, error)
    }

    // 添加过滤后的历史消息
    filteredMessages.forEach(msg => {
      if (msg.type === 'user') {
        let userContent = msg.content

        // 如果有用户信息，在消息内容后添加用户标识
        if (userInfo && userInfo.username && userInfo.employeeId) {
          // 基础用户信息
          userContent = `${msg.content}##@##${userInfo.username}##@##${userInfo.employeeId}`

          // 如果是业务域，还需要添加业务库信息
          if (this.module === 'business' && msg.selectedDatabase) {
            userContent = `${msg.content}##@##${userInfo.username}##@##${userInfo.employeeId}##@##${msg.selectedDatabase}`
            console.log(`[${this.module}] 业务域消息已添加用户信息和业务库:`, userContent)
          }
          // 如果是职能域，还需要添加职能功能信息
          else if (this.module === 'function' && msg.selectedFunction) {
            userContent = `${msg.content}##@##${userInfo.username}##@##${userInfo.employeeId}##@##${msg.selectedFunction}`
            console.log(`[${this.module}] 职能域消息已添加用户信息和职能功能:`, userContent)
          } else {
            console.log(`[${this.module}] 用户消息已添加用户信息:`, userContent)
          }
        } else {
          console.log(`[${this.module}] 用户信息不完整，未添加用户标识:`, userInfo)
        }

        requestMessages.push({
          role: 'user',
          content: userContent
        })
      } else if (msg.type === 'ai') {
        requestMessages.push({
          role: 'assistant',
          content: msg.content
        })
      }
    })

    return requestMessages
  }

  /**
   * 根据配置的历史轮数过滤消息
   * @param {Array} messages - 原始消息数组
   * @returns {Array} 过滤后的消息数组
   */
  filterMessagesByHistoryTurns(messages) {
    const historyTurns = this.config.historyTurns || 0

    // 如果设置为0，不携带历史对话
    if (historyTurns === 0) {
      // 只保留最后一条用户消息
      const lastUserMessage = messages.filter(msg => msg.type === 'user').pop()
      return lastUserMessage ? [lastUserMessage] : []
    }

    // 如果设置为-1，携带全部历史对话
    if (historyTurns === -1) {
      return messages
    }

    // 按轮数限制历史对话
    // 一轮对话 = 一条用户消息 + 一条AI回复
    const pairs = []
    let currentPair = null

    // 将消息按对话轮次分组
    messages.forEach(msg => {
      if (msg.type === 'user') {
        // 如果有未完成的对话轮次，先保存
        if (currentPair && currentPair.user) {
          pairs.push(currentPair)
        }
        // 开始新的对话轮次
        currentPair = { user: msg, ai: null }
      } else if (msg.type === 'ai' && currentPair) {
        // 完成当前对话轮次
        currentPair.ai = msg
      }
    })

    // 保存最后一个对话轮次（可能未完成）
    if (currentPair) {
      pairs.push(currentPair)
    }

    // 取最后N轮对话
    const selectedPairs = pairs.slice(-historyTurns)

    // 重新组装消息数组
    const result = []
    selectedPairs.forEach(pair => {
      if (pair.user) {
        result.push(pair.user)
      }
      if (pair.ai) {
        result.push(pair.ai)
      }
    })

    return result
  }

  /**
   * 发送HTTP请求
   * @param {Object} requestBody - 请求体
   * @returns {Promise<Object>} 响应对象
   */
  async makeRequest(requestBody) {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout)

    try {
      const response = await fetch(`${this.config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(`HTTP ${response.status}: ${errorData.error?.message || response.statusText}`)
      }

      return await response.json()

    } catch (error) {
      clearTimeout(timeoutId)

      if (error.name === 'AbortError') {
        throw new Error('请求超时，请稍后重试')
      }

      throw error
    }
  }

  /**
   * 发送流式HTTP请求
   * @param {Object} requestBody - 请求体
   * @param {Function} onChunk - 接收数据块的回调函数
   * @param {Function} onReasoning - 接收推理过程的回调函数
   * @returns {Promise<string>} 完整的回复内容
   */
  async makeStreamRequest(requestBody, onChunk, onReasoning) {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout)

    try {
      const response = await fetch(`${this.config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(`HTTP ${response.status}: ${errorData.error?.message || response.statusText}`)
      }

      // 处理流式响应
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let fullContent = ''
      let buffer = ''
      let reasoningContent = ''
      let textContent = ''
      let isReasoningPhase = true

      try {
        while (true) {
          const { done, value } = await reader.read()

          if (done) break

          // 解码数据块
          buffer += decoder.decode(value, { stream: true })

          // 处理可能包含多个事件的缓冲区
          const lines = buffer.split('\n')
          buffer = lines.pop() || '' // 保留最后一个可能不完整的行

          for (const line of lines) {
            if (line.trim() === '') continue
            if (line.trim() === 'data: [DONE]') continue

            if (line.startsWith('data: ')) {
              try {
                const jsonStr = line.slice(6) // 移除 'data: ' 前缀
                const data = JSON.parse(jsonStr)

                // 检查是否是新格式的响应（包含reasoning和text）
                const messageContent = data.choices?.[0]?.message?.content
                if (Array.isArray(messageContent)) {
                  // 新格式：处理包含reasoning和text的数组
                  for (const contentItem of messageContent) {
                    if (contentItem.type === 'reasoning' && contentItem.reasoning?.content) {
                      const reasoningChunk = contentItem.reasoning.content
                      reasoningContent += reasoningChunk

                      // 调用推理过程回调
                      if (onReasoning && isReasoningPhase) {
                        onReasoning(reasoningChunk, reasoningContent)
                      }
                    } else if (contentItem.type === 'text' && contentItem.text?.content) {
                      // 切换到文本阶段
                      if (isReasoningPhase) {
                        isReasoningPhase = false
                        console.log(`[${this.module}] 推理阶段完成，开始文本输出`)
                      }

                      const textChunk = contentItem.text.content
                      textContent += textChunk
                      fullContent = textContent // 最终内容只包含text部分

                      // 调用文本内容回调
                      if (onChunk) {
                        onChunk(textChunk, fullContent)
                      }
                    }
                  }
                } else {
                  // 兼容旧格式：直接处理delta.content
                  const content = data.choices?.[0]?.delta?.content
                  if (content !== null && content !== undefined) {
                    // 简单的类型检查
                    if (typeof content === 'object') {
                      console.error('流式响应中检测到对象类型内容，这不应该发生:', content)
                      const stringContent = JSON.stringify(content)
                      fullContent += stringContent
                      if (onChunk) {
                        onChunk(stringContent, fullContent)
                      }
                    } else {
                      const stringContent = String(content)
                      fullContent += stringContent
                      if (onChunk) {
                        onChunk(stringContent, fullContent)
                      }
                    }
                  }
                }
              } catch (parseError) {
                console.warn('解析流式数据失败:', parseError, line)
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
      }

      return fullContent

    } catch (error) {
      clearTimeout(timeoutId)

      if (error.name === 'AbortError') {
        throw new Error('请求超时，请稍后重试')
      }

      throw error
    }
  }

  /**
   * 解析AI响应
   * @param {Object} response - API响应对象
   * @returns {string} AI回复内容
   */
  parseResponse(response) {
    if (!response.choices || response.choices.length === 0) {
      throw new Error('AI响应格式错误：没有找到回复内容')
    }

    const choice = response.choices[0]
    const content = choice.message?.content

    if (content === null || content === undefined) {
      throw new Error('AI响应格式错误：回复内容为空')
    }

    // 简单的类型检查和转换
    if (typeof content === 'object') {
      console.error('AI返回了对象类型的内容，这不应该发生:', content)
      try {
        return JSON.stringify(content, null, 2)
      } catch (error) {
        console.error('对象转换失败:', error)
        return '内容格式错误：无法解析AI响应'
      }
    }

    // 确保是字符串并清理
    const stringContent = String(content).trim()

    if (!stringContent) {
      throw new Error('AI响应格式错误：内容为空字符串')
    }

    return stringContent
  }

  /**
   * 解析包含思维链的内容
   * @param {string} content - AI回复的完整内容
   * @returns {Object} 包含思维过程和最终答案的对象
   */
  parseThinkingContent(content) {
    // 常见的思维链标记模式
    const thinkingPatterns = [
      // <thinking>...</thinking> 格式
      /<thinking>([\s\S]*?)<\/thinking>/gi,
      // 【思考】...【/思考】格式
      /【思考】([\s\S]*?)【\/思考】/gi,
      // [思考]...[/思考] 格式
      /\[思考\]([\s\S]*?)\[\/思考\]/gi,
      // <think>...</think> 格式
      /<think>([\s\S]*?)<\/think>/gi,
      // 思考过程：... 格式
      /思考过程[：:]([\s\S]*?)(?=\n\n|$)/gi,
      // Let me think... 格式
      /Let me think[\.:]?([\s\S]*?)(?=\n\n|Answer:|Final answer:|$)/gi,
    ]

    let thinking = null
    let finalAnswer = content

    // 尝试匹配各种思维链格式
    for (const pattern of thinkingPatterns) {
      const matches = content.match(pattern)
      if (matches && matches.length > 0) {
        // 提取思维过程
        thinking = matches.map(match => {
          return match.replace(pattern, '$1').trim()
        }).join('\n\n')

        // 移除思维过程，保留最终答案
        finalAnswer = content.replace(pattern, '').trim()
        break
      }
    }

    // 如果没有找到明确的思维链标记，尝试其他启发式方法
    if (!thinking) {
      const result = this.extractImplicitThinking(content)
      thinking = result.thinking
      finalAnswer = result.answer
    }

    return {
      thinking: thinking,
      finalAnswer: finalAnswer || content
    }
  }

  /**
   * 提取隐式的思维过程（当没有明确标记时）
   * @param {string} content - 内容
   * @returns {Object} 思维过程和答案
   */
  extractImplicitThinking(content) {
    // 查找可能的思维过程指示词
    const thinkingIndicators = [
      '让我想想',
      '首先',
      '分析一下',
      '考虑到',
      '根据',
      '因此',
      '所以',
      '综上所述',
      'First,',
      'Let me',
      'I think',
      'Based on',
      'Therefore',
      'In conclusion'
    ]

    const lines = content.split('\n')
    let thinkingLines = []
    let answerLines = []
    let isThinking = false

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()

      // 检查是否包含思维指示词
      const hasThinkingIndicator = thinkingIndicators.some(indicator =>
        line.includes(indicator)
      )

      // 如果是问号结尾或者包含思维指示词，可能是思维过程
      if (hasThinkingIndicator || line.endsWith('?') || line.includes('...')) {
        isThinking = true
        thinkingLines.push(line)
      } else if (isThinking && (line.startsWith('答案') || line.startsWith('Answer') || line.startsWith('结论'))) {
        // 遇到答案标记，切换到答案模式
        isThinking = false
        answerLines.push(line)
      } else if (!isThinking) {
        answerLines.push(line)
      } else {
        thinkingLines.push(line)
      }
    }

    return {
      thinking: thinkingLines.length > 0 ? thinkingLines.join('\n') : null,
      answer: answerLines.length > 0 ? answerLines.join('\n') : content
    }
  }

  /**
   * 获取错误信息
   * @param {Error} error - 错误对象
   * @returns {string} 用户友好的错误信息
   */
  getErrorMessage(error) {
    const message = error.message || '未知错误'

    // 常见错误的用户友好提示
    if (message.includes('401')) {
      return 'API密钥无效，请检查配置'
    } else if (message.includes('429')) {
      return 'API调用频率过高，请稍后重试'
    } else if (message.includes('timeout') || message.includes('超时')) {
      return '请求超时，请检查网络连接或稍后重试'
    } else if (message.includes('network') || message.includes('fetch')) {
      return '网络连接失败，请检查网络设置'
    } else {
      return `AI服务错误: ${message}`
    }
  }

  /**
   * 生成模拟推理过程
   * @param {Array} messages - 消息历史数组
   * @param {Function} onReasoning - 推理过程回调函数
   */
  async generateMockReasoningProcess(messages, onReasoning) {
    const lastMessage = messages[messages.length - 1]
    const userContent = lastMessage?.content || ''

    // 根据问题类型和模块生成推理步骤
    const reasoningSteps = this.generateReasoningSteps(userContent)

    console.log(`[${this.module}] 开始生成推理过程，共${reasoningSteps.length}步`)

    // 逐步发送推理过程
    for (let i = 0; i < reasoningSteps.length; i++) {
      const step = reasoningSteps[i]

      // 发送步骤开始状态
      onReasoning({
        type: 'step_start',
        stepIndex: i,
        step: {
          ...step,
          status: 'thinking',
          startTime: Date.now()
        }
      })

      // 模拟思考时间
      await new Promise(resolve => setTimeout(resolve, step.duration || 1000))

      // 发送步骤完成状态
      onReasoning({
        type: 'step_complete',
        stepIndex: i,
        step: {
          ...step,
          status: 'completed',
          endTime: Date.now()
        }
      })

      // 步骤间的短暂间隔
      await new Promise(resolve => setTimeout(resolve, 200))
    }

    // 发送推理完成信号
    onReasoning({
      type: 'reasoning_complete',
      totalSteps: reasoningSteps.length
    })

    console.log(`[${this.module}] 推理过程生成完成`)
  }

  /**
   * 根据问题内容和模块生成推理步骤
   * @param {string} userContent - 用户问题内容
   * @returns {Array} 推理步骤数组
   */
  generateReasoningSteps(userContent) {
    // 基础推理步骤模板
    const baseSteps = {
      knowledge: [
        {
          title: '问题理解',
          description: '分析用户问题的核心需求和关键信息',
          icon: '🤔',
          duration: 800
        },
        {
          title: '知识检索',
          description: '从知识库中搜索相关信息和最佳实践',
          icon: '🔍',
          duration: 1200
        },
        {
          title: '信息整合',
          description: '整合检索到的信息，构建完整的知识框架',
          icon: '🧩',
          duration: 1000
        },
        {
          title: '答案生成',
          description: '基于整合的信息生成准确、有用的回复',
          icon: '✨',
          duration: 1500
        }
      ],
      business: [
        {
          title: '业务分析',
          description: '分析业务问题的背景和影响范围',
          icon: '📊',
          duration: 1000
        },
        {
          title: '方案评估',
          description: '评估可能的解决方案和实施路径',
          icon: '⚖️',
          duration: 1300
        },
        {
          title: '风险评估',
          description: '识别潜在风险和应对策略',
          icon: '⚠️',
          duration: 900
        },
        {
          title: '建议生成',
          description: '生成具体的业务建议和行动计划',
          icon: '💡',
          duration: 1200
        }
      ],
      function: [
        {
          title: '功能理解',
          description: '理解用户的功能需求和使用场景',
          icon: '⚙️',
          duration: 700
        },
        {
          title: '技术分析',
          description: '分析技术实现方案和可行性',
          icon: '🔧',
          duration: 1100
        },
        {
          title: '步骤规划',
          description: '规划具体的操作步骤和流程',
          icon: '📋',
          duration: 900
        },
        {
          title: '指导生成',
          description: '生成详细的操作指导和注意事项',
          icon: '📖',
          duration: 1000
        }
      ]
    }

    // 获取当前模块的推理步骤
    let steps = baseSteps[this.module] || baseSteps.knowledge

    // 根据问题内容动态调整推理步骤
    steps = this.customizeReasoningSteps(steps, userContent)

    return steps
  }

  /**
   * 根据问题内容定制推理步骤
   * @param {Array} steps - 基础推理步骤
   * @param {string} userContent - 用户问题内容
   * @returns {Array} 定制后的推理步骤
   */
  customizeReasoningSteps(steps, userContent) {
    const content = userContent.toLowerCase()

    // 如果是比较复杂的问题，增加额外的推理步骤
    if (content.includes('如何') || content.includes('怎么') || content.includes('方法')) {
      steps = [...steps]
      steps.splice(2, 0, {
        title: '方法探索',
        description: '探索多种可能的解决方法和途径',
        icon: '🔍',
        duration: 1000
      })
    }

    // 如果是比较问题，增加对比分析步骤
    if (content.includes('比较') || content.includes('对比') || content.includes('区别')) {
      steps = [...steps]
      steps.splice(1, 0, {
        title: '对比分析',
        description: '分析不同选项的优缺点和适用场景',
        icon: '⚖️',
        duration: 1200
      })
    }

    // 如果是故障问题，增加诊断步骤
    if (content.includes('问题') || content.includes('错误') || content.includes('故障')) {
      steps = [...steps]
      steps.splice(1, 0, {
        title: '问题诊断',
        description: '诊断问题的根本原因和影响范围',
        icon: '🔍',
        duration: 1100
      })
    }

    return steps
  }
}

/**
 * 创建AI服务实例
 * @param {string} module - 模块名称
 * @returns {AIService} AI服务实例
 */
export const createAIService = (module) => {
  return new AIService(module)
}

/**
 * 模块AI服务实例缓存
 */
const aiServiceCache = {}

/**
 * 获取AI服务实例（带缓存）
 * @param {string} module - 模块名称
 * @returns {AIService} AI服务实例
 */
export const getAIService = (module) => {
  if (!aiServiceCache[module]) {
    aiServiceCache[module] = new AIService(module)
  }
  return aiServiceCache[module]
}

// 默认导出
export default {
  AIService,
  createAIService,
  getAIService
}
